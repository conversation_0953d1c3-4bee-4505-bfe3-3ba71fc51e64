module.exports = {

"[project]/src/messages/en.json (json)": ((__turbopack_context__) => {

__turbopack_context__.v(JSON.parse("{\"navigation\":{\"home\":\"Home\",\"productPage\":\"Product Page\",\"contactUs\":\"Contact Us\",\"getStarted\":\"Get Started\",\"language\":\"ENG\"},\"hero\":{\"trustedPlatform\":\"100% TRUSTED PLATFORM\",\"title\":\"POWERING GLOBAL PAYMENTS WITH\",\"titleHighlight\":\"BLOCKCHAIN\",\"description\":\"Praith Is A Blockchain-Powered Crypto Payment Gateway That Solves The Traditional Payment Problems. Our Platform Enables Fast, Low-Fee, And Borderless Transactions Using Stablecoins On The Tron And Binance Smart Chain (BEP-20) Networks.\",\"getStarted\":\"Get Started\"},\"cryptocurrencies\":{\"tether\":\"Tether\",\"bitcoin\":\"Bitcoin\",\"tron\":\"Tron\",\"ethereum\":\"Ethereum\",\"litecoin\":\"Litecoin\"}}"));}),

};