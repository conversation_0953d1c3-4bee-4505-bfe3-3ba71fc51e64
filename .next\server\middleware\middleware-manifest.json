{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_cd76b067._.js", "server/edge/chunks/node_modules_@formatjs_intl-localematcher_lib_f4580406._.js", "server/edge/chunks/node_modules_75a74226._.js", "server/edge/chunks/[root-of-the-server]__b787568c._.js", "server/edge/chunks/edge-wrapper_a1e6df54.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/(/?index|/?index\\\\.json)?", "originalSource": "/"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/(de|en|es|fr)/:path*{(\\\\.json)}?", "originalSource": "/(de|en|es|fr)/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "vsZ7hzY9a2t6t4Ge4oH8p6ZtCDHcFDu1/4E0YZ6lqME=", "__NEXT_PREVIEW_MODE_ID": "36caea9a7ae5ec7d48f30216f990649a", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "059c746b891124c404fa1d0d5a3a98bf8087c72dab08842c667c74f5ea0eb964", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "9af4521384cd0b1daced7ca3c064b5bab0fdbe60edf2bf0efc43b143a9728d03"}}}, "instrumentation": null, "functions": {}}