{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/praith/src/i18n/routing.ts"], "sourcesContent": ["import {defineRouting} from 'next-intl/routing';\nimport {createNavigation} from 'next-intl/navigation';\n \nexport const routing = defineRouting({\n  // A list of all locales that are supported\n  locales: ['en', 'es', 'fr', 'de'],\n \n  // Used when no locale matches\n  defaultLocale: 'en',\n\n  // The `pathnames` object holds pairs of internal and\n  // external paths. Based on the locale, the external\n  // paths are rewritten to the shared, internal ones.\n  pathnames: {\n    // If all locales use the same pathname, a single\n    // external path can be provided for all locales\n    '/': '/',\n    '/about': {\n      en: '/about',\n      es: '/acerca-de',\n      fr: '/a-propos',\n      de: '/uber-uns'\n    }\n  }\n});\n \n// Lightweight wrappers around Next.js' navigation APIs\n// that will consider the routing configuration\nexport const {Link, redirect, usePathname, useRouter} =\n  createNavigation(routing);\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;;;AAEO,MAAM,UAAU,CAAA,GAAA,kOAAA,CAAA,gBAAa,AAAD,EAAE;IACnC,2CAA2C;IAC3C,SAAS;QAAC;QAAM;QAAM;QAAM;KAAK;IAEjC,8BAA8B;IAC9B,eAAe;IAEf,qDAAqD;IACrD,oDAAoD;IACpD,oDAAoD;IACpD,WAAW;QACT,iDAAiD;QACjD,gDAAgD;QAChD,KAAK;QACL,UAAU;YACR,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;IACF;AACF;AAIO,MAAM,EAAC,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAC,GACnD,CAAA,GAAA,8PAAA,CAAA,mBAAgB,AAAD,EAAE", "debugId": null}}, {"offset": {"line": 46, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/praith/src/components/Navigation.tsx"], "sourcesContent": ["'use client';\n\nimport { useTranslations } from 'next-intl';\nimport { Link } from '@/i18n/routing';\nimport Image from 'next/image';\n\nexport default function Navigation() {\n  const t = useTranslations('navigation');\n\n  return (\n    <nav className=\"flex items-center justify-between px-6 py-4 bg-black text-white\">\n      {/* Logo */}\n      <div className=\"flex items-center\">\n        <div className=\"text-2xl font-bold\">\n          <span className=\"text-green-400\">∞</span>\n          <span className=\"ml-1\">Praith</span>\n        </div>\n      </div>\n\n      {/* Navigation Links */}\n      <div className=\"hidden md:flex items-center space-x-8\">\n        <Link href=\"/\" className=\"hover:text-green-400 transition-colors\">\n          {t('home')}\n        </Link>\n        <Link href=\"/product\" className=\"hover:text-green-400 transition-colors\">\n          {t('productPage')}\n        </Link>\n        <Link href=\"/contact\" className=\"hover:text-green-400 transition-colors\">\n          {t('contactUs')}\n        </Link>\n      </div>\n\n      {/* Right side buttons */}\n      <div className=\"flex items-center space-x-4\">\n        <button className=\"bg-green-400 text-black px-6 py-2 rounded-md font-medium hover:bg-green-500 transition-colors\">\n          {t('getStarted')}\n        </button>\n        <div className=\"flex items-center space-x-2\">\n          <span className=\"text-sm\">{t('language')}</span>\n          <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 9l-7 7-7-7\" />\n          </svg>\n        </div>\n      </div>\n    </nav>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAMe,SAAS;IACtB,MAAM,IAAI,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD,EAAE;IAE1B,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAK,WAAU;sCAAiB;;;;;;sCACjC,8OAAC;4BAAK,WAAU;sCAAO;;;;;;;;;;;;;;;;;0BAK3B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,sHAAA,CAAA,OAAI;wBAAC,MAAK;wBAAI,WAAU;kCACtB,EAAE;;;;;;kCAEL,8OAAC,sHAAA,CAAA,OAAI;wBAAC,MAAK;wBAAW,WAAU;kCAC7B,EAAE;;;;;;kCAEL,8OAAC,sHAAA,CAAA,OAAI;wBAAC,MAAK;wBAAW,WAAU;kCAC7B,EAAE;;;;;;;;;;;;0BAKP,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAO,WAAU;kCACf,EAAE;;;;;;kCAEL,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAK,WAAU;0CAAW,EAAE;;;;;;0CAC7B,8OAAC;gCAAI,WAAU;gCAAU,MAAK;gCAAO,QAAO;gCAAe,SAAQ;0CACjE,cAAA,8OAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMjF", "debugId": null}}, {"offset": {"line": 194, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/praith/src/components/CryptoLogos.tsx"], "sourcesContent": ["'use client';\n\nimport { useTranslations } from 'next-intl';\n\nexport default function CryptoLogos() {\n  const t = useTranslations('cryptocurrencies');\n\n  const cryptos = [\n    {\n      name: t('tether'),\n      symbol: 'USDT',\n      icon: '₮',\n      color: 'text-green-400'\n    },\n    {\n      name: t('bitcoin'),\n      symbol: 'BTC',\n      icon: '₿',\n      color: 'text-orange-400'\n    },\n    {\n      name: t('tron'),\n      symbol: 'TRX',\n      icon: 'T',\n      color: 'text-red-400'\n    },\n    {\n      name: t('ethereum'),\n      symbol: 'ETH',\n      icon: 'Ξ',\n      color: 'text-blue-400'\n    },\n    {\n      name: t('litecoin'),\n      symbol: 'LTC',\n      icon: 'Ł',\n      color: 'text-gray-400'\n    }\n  ];\n\n  return (\n    <div className=\"container mx-auto px-6\">\n      <div className=\"flex items-center justify-center space-x-12 lg:space-x-16\">\n        {cryptos.map((crypto, index) => (\n          <div key={index} className=\"flex items-center space-x-3 opacity-70 hover:opacity-100 transition-opacity\">\n            <div className={`text-2xl font-bold ${crypto.color}`}>\n              {crypto.icon}\n            </div>\n            <div className=\"text-white\">\n              <div className=\"font-semibold text-sm\">{crypto.name}</div>\n              <div className=\"text-xs text-gray-400\">{crypto.symbol}</div>\n            </div>\n          </div>\n        ))}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS;IACtB,MAAM,IAAI,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD,EAAE;IAE1B,MAAM,UAAU;QACd;YACE,MAAM,EAAE;YACR,QAAQ;YACR,MAAM;YACN,OAAO;QACT;QACA;YACE,MAAM,EAAE;YACR,QAAQ;YACR,MAAM;YACN,OAAO;QACT;QACA;YACE,MAAM,EAAE;YACR,QAAQ;YACR,MAAM;YACN,OAAO;QACT;QACA;YACE,MAAM,EAAE;YACR,QAAQ;YACR,MAAM;YACN,OAAO;QACT;QACA;YACE,MAAM,EAAE;YACR,QAAQ;YACR,MAAM;YACN,OAAO;QACT;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACZ,QAAQ,GAAG,CAAC,CAAC,QAAQ,sBACpB,8OAAC;oBAAgB,WAAU;;sCACzB,8OAAC;4BAAI,WAAW,CAAC,mBAAmB,EAAE,OAAO,KAAK,EAAE;sCACjD,OAAO,IAAI;;;;;;sCAEd,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAAyB,OAAO,IAAI;;;;;;8CACnD,8OAAC;oCAAI,WAAU;8CAAyB,OAAO,MAAM;;;;;;;;;;;;;mBAN/C;;;;;;;;;;;;;;;AAapB", "debugId": null}}, {"offset": {"line": 297, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/praith/src/components/GlobalVisualization.tsx"], "sourcesContent": ["'use client';\n\nexport default function GlobalVisualization() {\n  return (\n    <div className=\"relative w-full h-96 lg:h-[500px]\">\n      {/* World Map Background (Dotted Pattern) */}\n      <div className=\"absolute inset-0 opacity-30\">\n        <svg\n          viewBox=\"0 0 800 500\"\n          className=\"w-full h-full\"\n          fill=\"none\"\n          xmlns=\"http://www.w3.org/2000/svg\"\n        >\n          {/* Dotted world map pattern */}\n          <defs>\n            <pattern id=\"dots\" x=\"0\" y=\"0\" width=\"20\" height=\"20\" patternUnits=\"userSpaceOnUse\">\n              <circle cx=\"10\" cy=\"10\" r=\"1\" fill=\"#374151\" />\n            </pattern>\n          </defs>\n          <rect width=\"800\" height=\"500\" fill=\"url(#dots)\" />\n          \n          {/* Simplified continent shapes with dots */}\n          <g fill=\"#4B5563\">\n            {/* North America */}\n            <circle cx=\"150\" cy=\"120\" r=\"2\" />\n            <circle cx=\"170\" cy=\"140\" r=\"2\" />\n            <circle cx=\"190\" cy=\"160\" r=\"2\" />\n            \n            {/* Europe */}\n            <circle cx=\"400\" cy=\"100\" r=\"2\" />\n            <circle cx=\"420\" cy=\"120\" r=\"2\" />\n            \n            {/* Asia */}\n            <circle cx=\"600\" cy=\"140\" r=\"2\" />\n            <circle cx=\"650\" cy=\"160\" r=\"2\" />\n            \n            {/* Africa */}\n            <circle cx=\"450\" cy=\"250\" r=\"2\" />\n            <circle cx=\"470\" cy=\"280\" r=\"2\" />\n            \n            {/* South America */}\n            <circle cx=\"250\" cy=\"300\" r=\"2\" />\n            <circle cx=\"270\" cy=\"350\" r=\"2\" />\n            \n            {/* Australia */}\n            <circle cx=\"700\" cy=\"350\" r=\"2\" />\n          </g>\n        </svg>\n      </div>\n\n      {/* Central Hub */}\n      <div className=\"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2\">\n        <div className=\"relative\">\n          {/* Central green circle with icons */}\n          <div className=\"w-20 h-20 bg-green-400 rounded-full flex items-center justify-center relative\">\n            <div className=\"w-8 h-8 bg-white rounded-sm flex items-center justify-center\">\n              <span className=\"text-green-600 font-bold text-lg\">₿</span>\n            </div>\n            <div className=\"absolute -top-2 -right-2 w-8 h-8 bg-green-500 rounded-full flex items-center justify-center\">\n              <span className=\"text-white text-xs\">⚡</span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Floating Crypto Icons */}\n      <div className=\"absolute top-16 left-16\">\n        <div className=\"w-12 h-12 bg-red-500 rounded-full flex items-center justify-center animate-pulse\">\n          <span className=\"text-white font-bold\">₿</span>\n        </div>\n      </div>\n\n      <div className=\"absolute top-32 right-20\">\n        <div className=\"w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center animate-pulse\">\n          <span className=\"text-white font-bold\">Ξ</span>\n        </div>\n      </div>\n\n      <div className=\"absolute bottom-32 left-20\">\n        <div className=\"w-12 h-12 bg-teal-500 rounded-full flex items-center justify-center animate-pulse\">\n          <span className=\"text-white font-bold\">T</span>\n        </div>\n      </div>\n\n      <div className=\"absolute bottom-20 right-32\">\n        <div className=\"w-12 h-12 bg-orange-500 rounded-full flex items-center justify-center animate-pulse\">\n          <span className=\"text-white font-bold\">Ł</span>\n        </div>\n      </div>\n\n      {/* Orbital Rings */}\n      <div className=\"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2\">\n        <div className=\"w-64 h-64 border border-green-400 rounded-full opacity-30 animate-spin\" style={{animationDuration: '20s'}}></div>\n        <div className=\"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-80 h-80 border border-green-400 rounded-full opacity-20 animate-spin\" style={{animationDuration: '30s', animationDirection: 'reverse'}}></div>\n      </div>\n\n      {/* Connection Lines */}\n      <svg className=\"absolute inset-0 w-full h-full pointer-events-none\">\n        <defs>\n          <linearGradient id=\"lineGradient\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"0%\">\n            <stop offset=\"0%\" stopColor=\"#10B981\" stopOpacity=\"0\" />\n            <stop offset=\"50%\" stopColor=\"#10B981\" stopOpacity=\"0.8\" />\n            <stop offset=\"100%\" stopColor=\"#10B981\" stopOpacity=\"0\" />\n          </linearGradient>\n        </defs>\n        \n        {/* Animated connection lines */}\n        <line x1=\"50%\" y1=\"50%\" x2=\"10%\" y2=\"20%\" stroke=\"url(#lineGradient)\" strokeWidth=\"2\" className=\"animate-pulse\" />\n        <line x1=\"50%\" y1=\"50%\" x2=\"90%\" y2=\"30%\" stroke=\"url(#lineGradient)\" strokeWidth=\"2\" className=\"animate-pulse\" />\n        <line x1=\"50%\" y1=\"50%\" x2=\"20%\" y2=\"80%\" stroke=\"url(#lineGradient)\" strokeWidth=\"2\" className=\"animate-pulse\" />\n        <line x1=\"50%\" y1=\"50%\" x2=\"80%\" y2=\"85%\" stroke=\"url(#lineGradient)\" strokeWidth=\"2\" className=\"animate-pulse\" />\n      </svg>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBACC,SAAQ;oBACR,WAAU;oBACV,MAAK;oBACL,OAAM;;sCAGN,8OAAC;sCACC,cAAA,8OAAC;gCAAQ,IAAG;gCAAO,GAAE;gCAAI,GAAE;gCAAI,OAAM;gCAAK,QAAO;gCAAK,cAAa;0CACjE,cAAA,8OAAC;oCAAO,IAAG;oCAAK,IAAG;oCAAK,GAAE;oCAAI,MAAK;;;;;;;;;;;;;;;;sCAGvC,8OAAC;4BAAK,OAAM;4BAAM,QAAO;4BAAM,MAAK;;;;;;sCAGpC,8OAAC;4BAAE,MAAK;;8CAEN,8OAAC;oCAAO,IAAG;oCAAM,IAAG;oCAAM,GAAE;;;;;;8CAC5B,8OAAC;oCAAO,IAAG;oCAAM,IAAG;oCAAM,GAAE;;;;;;8CAC5B,8OAAC;oCAAO,IAAG;oCAAM,IAAG;oCAAM,GAAE;;;;;;8CAG5B,8OAAC;oCAAO,IAAG;oCAAM,IAAG;oCAAM,GAAE;;;;;;8CAC5B,8OAAC;oCAAO,IAAG;oCAAM,IAAG;oCAAM,GAAE;;;;;;8CAG5B,8OAAC;oCAAO,IAAG;oCAAM,IAAG;oCAAM,GAAE;;;;;;8CAC5B,8OAAC;oCAAO,IAAG;oCAAM,IAAG;oCAAM,GAAE;;;;;;8CAG5B,8OAAC;oCAAO,IAAG;oCAAM,IAAG;oCAAM,GAAE;;;;;;8CAC5B,8OAAC;oCAAO,IAAG;oCAAM,IAAG;oCAAM,GAAE;;;;;;8CAG5B,8OAAC;oCAAO,IAAG;oCAAM,IAAG;oCAAM,GAAE;;;;;;8CAC5B,8OAAC;oCAAO,IAAG;oCAAM,IAAG;oCAAM,GAAE;;;;;;8CAG5B,8OAAC;oCAAO,IAAG;oCAAM,IAAG;oCAAM,GAAE;;;;;;;;;;;;;;;;;;;;;;;0BAMlC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BAEb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAK,WAAU;8CAAmC;;;;;;;;;;;0CAErD,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAK,WAAU;8CAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO7C,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAK,WAAU;kCAAuB;;;;;;;;;;;;;;;;0BAI3C,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAK,WAAU;kCAAuB;;;;;;;;;;;;;;;;0BAI3C,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAK,WAAU;kCAAuB;;;;;;;;;;;;;;;;0BAI3C,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAK,WAAU;kCAAuB;;;;;;;;;;;;;;;;0BAK3C,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;wBAAyE,OAAO;4BAAC,mBAAmB;wBAAK;;;;;;kCACxH,8OAAC;wBAAI,WAAU;wBAA+I,OAAO;4BAAC,mBAAmB;4BAAO,oBAAoB;wBAAS;;;;;;;;;;;;0BAI/N,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;kCACC,cAAA,8OAAC;4BAAe,IAAG;4BAAe,IAAG;4BAAK,IAAG;4BAAK,IAAG;4BAAO,IAAG;;8CAC7D,8OAAC;oCAAK,QAAO;oCAAK,WAAU;oCAAU,aAAY;;;;;;8CAClD,8OAAC;oCAAK,QAAO;oCAAM,WAAU;oCAAU,aAAY;;;;;;8CACnD,8OAAC;oCAAK,QAAO;oCAAO,WAAU;oCAAU,aAAY;;;;;;;;;;;;;;;;;kCAKxD,8OAAC;wBAAK,IAAG;wBAAM,IAAG;wBAAM,IAAG;wBAAM,IAAG;wBAAM,QAAO;wBAAqB,aAAY;wBAAI,WAAU;;;;;;kCAChG,8OAAC;wBAAK,IAAG;wBAAM,IAAG;wBAAM,IAAG;wBAAM,IAAG;wBAAM,QAAO;wBAAqB,aAAY;wBAAI,WAAU;;;;;;kCAChG,8OAAC;wBAAK,IAAG;wBAAM,IAAG;wBAAM,IAAG;wBAAM,IAAG;wBAAM,QAAO;wBAAqB,aAAY;wBAAI,WAAU;;;;;;kCAChG,8OAAC;wBAAK,IAAG;wBAAM,IAAG;wBAAM,IAAG;wBAAM,IAAG;wBAAM,QAAO;wBAAqB,aAAY;wBAAI,WAAU;;;;;;;;;;;;;;;;;;AAIxG", "debugId": null}}, {"offset": {"line": 769, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/praith/src/components/HeroSection.tsx"], "sourcesContent": ["'use client';\n\nimport { useTranslations } from 'next-intl';\nimport Navigation from './Navigation';\nimport CryptoLogos from './CryptoLogos';\nimport GlobalVisualization from './GlobalVisualization';\n\nexport default function HeroSection() {\n  const t = useTranslations('hero');\n\n  return (\n    <div className=\"min-h-screen bg-black text-white relative overflow-hidden\">\n      {/* Navigation */}\n      <Navigation />\n      \n      {/* Main Hero Content */}\n      <div className=\"container mx-auto px-6 py-16 relative z-10\">\n        <div className=\"grid lg:grid-cols-2 gap-12 items-center\">\n          {/* Left Content */}\n          <div className=\"space-y-8\">\n            {/* Trust Badge */}\n            <div className=\"flex items-center space-x-2 text-orange-400\">\n              <div className=\"w-4 h-4 bg-orange-400 rounded-full flex items-center justify-center\">\n                <span className=\"text-black text-xs font-bold\">⚠</span>\n              </div>\n              <span className=\"text-sm font-medium tracking-wide\">\n                {t('trustedPlatform')}\n              </span>\n            </div>\n\n            {/* Main Heading */}\n            <div className=\"space-y-4\">\n              <h1 className=\"text-4xl lg:text-6xl font-bold leading-tight\">\n                {t('title')}\n                <br />\n                <span className=\"text-green-400\">{t('titleHighlight')}</span>\n              </h1>\n            </div>\n\n            {/* Description */}\n            <p className=\"text-gray-300 text-lg leading-relaxed max-w-2xl\">\n              {t('description')}\n            </p>\n\n            {/* CTA Button */}\n            <div className=\"pt-4\">\n              <button className=\"bg-green-400 text-black px-8 py-4 rounded-md font-semibold text-lg hover:bg-green-500 transition-colors\">\n                {t('getStarted')}\n              </button>\n            </div>\n          </div>\n\n          {/* Right Content - Global Visualization */}\n          <div className=\"relative\">\n            <GlobalVisualization />\n          </div>\n        </div>\n      </div>\n\n      {/* Crypto Logos Footer */}\n      <div className=\"absolute bottom-0 left-0 right-0 bg-gradient-to-r from-gray-900 to-gray-800 py-6\">\n        <CryptoLogos />\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAOe,SAAS;IACtB,MAAM,IAAI,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD,EAAE;IAE1B,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,gIAAA,CAAA,UAAU;;;;;0BAGX,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAA+B;;;;;;;;;;;sDAEjD,8OAAC;4CAAK,WAAU;sDACb,EAAE;;;;;;;;;;;;8CAKP,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAG,WAAU;;4CACX,EAAE;0DACH,8OAAC;;;;;0DACD,8OAAC;gDAAK,WAAU;0DAAkB,EAAE;;;;;;;;;;;;;;;;;8CAKxC,8OAAC;oCAAE,WAAU;8CACV,EAAE;;;;;;8CAIL,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAO,WAAU;kDACf,EAAE;;;;;;;;;;;;;;;;;sCAMT,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,yIAAA,CAAA,UAAmB;;;;;;;;;;;;;;;;;;;;;0BAM1B,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,iIAAA,CAAA,UAAW;;;;;;;;;;;;;;;;AAIpB", "debugId": null}}, {"offset": {"line": 940, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/praith/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 945, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/praith/node_modules/next-intl/dist/esm/development/react-client/index.js"], "sourcesContent": ["import { useFormatter as useFormatter$1, useTranslations as useTranslations$1 } from 'use-intl';\nexport * from 'use-intl';\n\n/**\n * This is the main entry file when non-'react-server'\n * environments import from 'next-intl'.\n *\n * Maintainer notes:\n * - Make sure this mirrors the API from 'react-server'.\n * - Make sure everything exported from this module is\n *   supported in all Next.js versions that are supported.\n */\n\n\n// eslint-disable-next-line @typescript-eslint/no-unsafe-function-type\nfunction callHook(name, hook) {\n  return (...args) => {\n    try {\n      return hook(...args);\n    } catch {\n      throw new Error(`Failed to call \\`${name}\\` because the context from \\`NextIntlClientProvider\\` was not found.\n\nThis can happen because:\n1) You intended to render this component as a Server Component, the render\n   failed, and therefore <PERSON><PERSON> attempted to render the component on the client\n   instead. If this is the case, check the console for server errors.\n2) You intended to render this component on the client side, but no context was found.\n   Learn more about this error here: https://next-intl.dev/docs/environments/server-client-components#missing-context` );\n    }\n  };\n}\nconst useTranslations = callHook('useTranslations', useTranslations$1);\nconst useFormatter = callHook('useFormatter', useFormatter$1);\n\nexport { useFormatter, useTranslations };\n"], "names": [], "mappings": ";;;;AAAA;;;AAGA;;;;;;;;CAQC,GAGD,sEAAsE;AACtE,SAAS,SAAS,IAAI,EAAE,IAAI;IAC1B,OAAO,CAAC,GAAG;QACT,IAAI;YACF,OAAO,QAAQ;QACjB,EAAE,OAAM;YACN,MAAM,IAAI,MAAM,CAAC,iBAAiB,EAAE,KAAK;;;;;;;qHAOsE,CAAC;QAClH;IACF;AACF;AACA,MAAM,kBAAkB,SAAS,mBAAmB,kKAAA,CAAA,kBAAiB;AACrE,MAAM,eAAe,SAAS,gBAAgB,kKAAA,CAAA,eAAc", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 984, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/praith/node_modules/next-intl/dist/esm/development/routing/defineRouting.js"], "sourcesContent": ["function defineRouting(config) {\n  if (config.domains) {\n    validateUniqueLocalesPerDomain(config.domains);\n  }\n  return config;\n}\nfunction validateUniqueLocalesPerDomain(domains) {\n  const domainsByLocale = new Map();\n  for (const {\n    domain,\n    locales\n  } of domains) {\n    for (const locale of locales) {\n      const localeDomains = domainsByLocale.get(locale) || new Set();\n      localeDomains.add(domain);\n      domainsByLocale.set(locale, localeDomains);\n    }\n  }\n  const duplicateLocaleMessages = Array.from(domainsByLocale.entries()).filter(([, localeDomains]) => localeDomains.size > 1).map(([locale, localeDomains]) => `- \"${locale}\" is used by: ${Array.from(localeDomains).join(', ')}`);\n  if (duplicateLocaleMessages.length > 0) {\n    console.warn('Locales are expected to be unique per domain, but found overlap:\\n' + duplicateLocaleMessages.join('\\n') + '\\nPlease see https://next-intl.dev/docs/routing#domains');\n  }\n}\n\nexport { defineRouting as default };\n"], "names": [], "mappings": ";;;AAAA,SAAS,cAAc,MAAM;IAC3B,IAAI,OAAO,OAAO,EAAE;QAClB,+BAA+B,OAAO,OAAO;IAC/C;IACA,OAAO;AACT;AACA,SAAS,+BAA+B,OAAO;IAC7C,MAAM,kBAAkB,IAAI;IAC5B,KAAK,MAAM,EACT,MAAM,EACN,OAAO,EACR,IAAI,QAAS;QACZ,KAAK,MAAM,UAAU,QAAS;YAC5B,MAAM,gBAAgB,gBAAgB,GAAG,CAAC,WAAW,IAAI;YACzD,cAAc,GAAG,CAAC;YAClB,gBAAgB,GAAG,CAAC,QAAQ;QAC9B;IACF;IACA,MAAM,0BAA0B,MAAM,IAAI,CAAC,gBAAgB,OAAO,IAAI,MAAM,CAAC,CAAC,GAAG,cAAc,GAAK,cAAc,IAAI,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC,QAAQ,cAAc,GAAK,CAAC,GAAG,EAAE,OAAO,cAAc,EAAE,MAAM,IAAI,CAAC,eAAe,IAAI,CAAC,OAAO;IAChO,IAAI,wBAAwB,MAAM,GAAG,GAAG;QACtC,QAAQ,IAAI,CAAC,uEAAuE,wBAAwB,IAAI,CAAC,QAAQ;IAC3H;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1020, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/praith/node_modules/next-intl/dist/esm/development/routing/config.js"], "sourcesContent": ["function receiveRoutingConfig(input) {\n  return {\n    ...input,\n    localePrefix: receiveLocalePrefixConfig(input.localePrefix),\n    localeCookie: receiveLocaleCookie(input.localeCookie),\n    localeDetection: input.localeDetection ?? true,\n    alternateLinks: input.alternateLinks ?? true\n  };\n}\nfunction receiveLocaleCookie(localeCookie) {\n  return localeCookie ?? true ? {\n    name: 'NEXT_LOCALE',\n    sameSite: 'lax',\n    ...(typeof localeCookie === 'object' && localeCookie)\n\n    // `path` needs to be provided based on a detected base path\n    // that depends on the environment when setting a cookie\n  } : false;\n}\nfunction receiveLocalePrefixConfig(localePrefix) {\n  return typeof localePrefix === 'object' ? localePrefix : {\n    mode: localePrefix || 'always'\n  };\n}\n\nexport { receiveRoutingConfig };\n"], "names": [], "mappings": ";;;AAAA,SAAS,qBAAqB,KAAK;IACjC,OAAO;QACL,GAAG,KAAK;QACR,cAAc,0BAA0B,MAAM,YAAY;QAC1D,cAAc,oBAAoB,MAAM,YAAY;QACpD,iBAAiB,MAAM,eAAe,IAAI;QAC1C,gBAAgB,MAAM,cAAc,IAAI;IAC1C;AACF;AACA,SAAS,oBAAoB,YAAY;IACvC,OAAO,gBAAgB,OAAO;QAC5B,MAAM;QACN,UAAU;QACV,GAAI,OAAO,iBAAiB,YAAY,YAAY;IAItD,IAAI;AACN;AACA,SAAS,0BAA0B,YAAY;IAC7C,OAAO,OAAO,iBAAiB,WAAW,eAAe;QACvD,MAAM,gBAAgB;IACxB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1049, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/praith/node_modules/next-intl/dist/esm/development/shared/use.js"], "sourcesContent": ["import * as react from 'react';\n\n// @ts-expect-error -- Ooof, Next.js doesn't make this easy.\n// `use` is only available in React 19 canary, but we can\n// use it in Next.js already as Next.js \"vendors\" a fixed\n// version of React. However, if we'd simply put `use` in\n// ESM code, then the build doesn't work since React does\n// not export `use` officially. Therefore, we have to use\n// something that is not statically analyzable. Once React\n// 19 is out, we can remove this in the next major version.\nvar use = react['use'.trim()];\n\nexport { use as default };\n"], "names": [], "mappings": ";;;AAAA;;AAEA,4DAA4D;AAC5D,yDAAyD;AACzD,yDAAyD;AACzD,yDAAyD;AACzD,yDAAyD;AACzD,yDAAyD;AACzD,0DAA0D;AAC1D,2DAA2D;AAC3D,IAAI,MAAM,qMAAK,CAAC,MAAM,IAAI,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1068, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/praith/node_modules/next-intl/dist/esm/development/navigation/shared/createSharedNavigationFns.js"], "sourcesContent": ["import { redirect, permanentRedirect } from 'next/navigation';\nimport { forwardRef } from 'react';\nimport { receiveRoutingConfig } from '../../routing/config.js';\nimport use from '../../shared/use.js';\nimport { isLocalizableHref, isPromise } from '../../shared/utils.js';\nimport BaseLink from './BaseLink.js';\nimport { validateReceivedConfig, serializeSearchParams, compileLocalizedPathname, applyPathnamePrefix, normalizeNameOrNameWithParams } from './utils.js';\nimport { jsx } from 'react/jsx-runtime';\n\n/**\n * Shared implementations for `react-server` and `react-client`\n */\nfunction createSharedNavigationFns(getLocale, routing) {\n  const config = receiveRoutingConfig(routing || {});\n  {\n    validateReceivedConfig(config);\n  }\n  const pathnames = config.pathnames;\n  function Link({\n    href,\n    locale,\n    ...rest\n  }, ref) {\n    let pathname, params;\n    if (typeof href === 'object') {\n      pathname = href.pathname;\n      // @ts-expect-error -- This is ok\n      params = href.params;\n    } else {\n      pathname = href;\n    }\n\n    // @ts-expect-error -- This is ok\n    const isLocalizable = isLocalizableHref(href);\n    const localePromiseOrValue = getLocale();\n    const curLocale = isPromise(localePromiseOrValue) ? use(localePromiseOrValue) : localePromiseOrValue;\n    const finalPathname = isLocalizable ? getPathname({\n      locale: locale || curLocale,\n      // @ts-expect-error -- This is ok\n      href: pathnames == null ? pathname : {\n        pathname,\n        params\n      },\n      // Always include a prefix when changing locales\n      forcePrefix: locale != null || undefined\n    }) : pathname;\n    return /*#__PURE__*/jsx(BaseLink, {\n      ref: ref\n      // @ts-expect-error -- This is ok\n      ,\n      href: typeof href === 'object' ? {\n        ...href,\n        pathname: finalPathname\n      } : finalPathname,\n      locale: locale,\n      localeCookie: config.localeCookie,\n      ...rest\n    });\n  }\n  const LinkWithRef = /*#__PURE__*/forwardRef(Link);\n  function getPathname(args) {\n    const {\n      forcePrefix,\n      href,\n      locale\n    } = args;\n    let pathname;\n    if (pathnames == null) {\n      if (typeof href === 'object') {\n        pathname = href.pathname;\n        if (href.query) {\n          pathname += serializeSearchParams(href.query);\n        }\n      } else {\n        pathname = href;\n      }\n    } else {\n      pathname = compileLocalizedPathname({\n        locale,\n        // @ts-expect-error -- This is ok\n        ...normalizeNameOrNameWithParams(href),\n        // @ts-expect-error -- This is ok\n        pathnames: config.pathnames\n      });\n    }\n    return applyPathnamePrefix(pathname, locale, config, forcePrefix);\n  }\n  function getRedirectFn(fn) {\n    /** @see https://next-intl.dev/docs/routing/navigation#redirect */\n    return function redirectFn(args, ...rest) {\n      return fn(getPathname(args), ...rest);\n    };\n  }\n  const redirect$1 = getRedirectFn(redirect);\n  const permanentRedirect$1 = getRedirectFn(permanentRedirect);\n  return {\n    config,\n    Link: LinkWithRef,\n    redirect: redirect$1,\n    permanentRedirect: permanentRedirect$1,\n    getPathname\n  };\n}\n\nexport { createSharedNavigationFns as default };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;AAEA;;CAEC,GACD,SAAS,0BAA0B,SAAS,EAAE,OAAO;IACnD,MAAM,SAAS,CAAA,GAAA,+KAAA,CAAA,uBAAoB,AAAD,EAAE,WAAW,CAAC;IAChD;QACE,CAAA,GAAA,2LAAA,CAAA,yBAAsB,AAAD,EAAE;IACzB;IACA,MAAM,YAAY,OAAO,SAAS;IAClC,SAAS,KAAK,EACZ,IAAI,EACJ,MAAM,EACN,GAAG,MACJ,EAAE,GAAG;QACJ,IAAI,UAAU;QACd,IAAI,OAAO,SAAS,UAAU;YAC5B,WAAW,KAAK,QAAQ;YACxB,iCAAiC;YACjC,SAAS,KAAK,MAAM;QACtB,OAAO;YACL,WAAW;QACb;QAEA,iCAAiC;QACjC,MAAM,gBAAgB,CAAA,GAAA,6KAAA,CAAA,oBAAiB,AAAD,EAAE;QACxC,MAAM,uBAAuB;QAC7B,MAAM,YAAY,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,wBAAwB,CAAA,GAAA,2KAAA,CAAA,UAAG,AAAD,EAAE,wBAAwB;QAChF,MAAM,gBAAgB,gBAAgB,YAAY;YAChD,QAAQ,UAAU;YAClB,iCAAiC;YACjC,MAAM,aAAa,OAAO,WAAW;gBACnC;gBACA;YACF;YACA,gDAAgD;YAChD,aAAa,UAAU,QAAQ;QACjC,KAAK;QACL,OAAO,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,8LAAA,CAAA,UAAQ,EAAE;YAChC,KAAK;YAGL,MAAM,OAAO,SAAS,WAAW;gBAC/B,GAAG,IAAI;gBACP,UAAU;YACZ,IAAI;YACJ,QAAQ;YACR,cAAc,OAAO,YAAY;YACjC,GAAG,IAAI;QACT;IACF;IACA,MAAM,cAAc,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC5C,SAAS,YAAY,IAAI;QACvB,MAAM,EACJ,WAAW,EACX,IAAI,EACJ,MAAM,EACP,GAAG;QACJ,IAAI;QACJ,IAAI,aAAa,MAAM;YACrB,IAAI,OAAO,SAAS,UAAU;gBAC5B,WAAW,KAAK,QAAQ;gBACxB,IAAI,KAAK,KAAK,EAAE;oBACd,YAAY,CAAA,GAAA,2LAAA,CAAA,wBAAqB,AAAD,EAAE,KAAK,KAAK;gBAC9C;YACF,OAAO;gBACL,WAAW;YACb;QACF,OAAO;YACL,WAAW,CAAA,GAAA,2LAAA,CAAA,2BAAwB,AAAD,EAAE;gBAClC;gBACA,iCAAiC;gBACjC,GAAG,CAAA,GAAA,2LAAA,CAAA,gCAA6B,AAAD,EAAE,KAAK;gBACtC,iCAAiC;gBACjC,WAAW,OAAO,SAAS;YAC7B;QACF;QACA,OAAO,CAAA,GAAA,2LAAA,CAAA,sBAAmB,AAAD,EAAE,UAAU,QAAQ,QAAQ;IACvD;IACA,SAAS,cAAc,EAAE;QACvB,gEAAgE,GAChE,OAAO,SAAS,WAAW,IAAI,EAAE,GAAG,IAAI;YACtC,OAAO,GAAG,YAAY,UAAU;QAClC;IACF;IACA,MAAM,aAAa,cAAc,kIAAA,CAAA,WAAQ;IACzC,MAAM,sBAAsB,cAAc,kIAAA,CAAA,oBAAiB;IAC3D,OAAO;QACL;QACA,MAAM;QACN,UAAU;QACV,mBAAmB;QACnB;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1173, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/praith/node_modules/next-intl/dist/esm/development/navigation/react-client/useBasePathname.js"], "sourcesContent": ["import { usePathname } from 'next/navigation';\nimport { useMemo } from 'react';\nimport { useLocale } from 'use-intl';\nimport { hasPathnamePrefixed, unprefixPathname, getLocalePrefix, getLocaleAsPrefix } from '../../shared/utils.js';\n\nfunction useBasePathname(config) {\n  // The types aren't entirely correct here. Outside of Next.js\n  // `useParams` can be called, but the return type is `null`.\n\n  // Notes on `useNextPathname`:\n  // - Types aren't entirely correct. Outside of Next.js the\n  //   hook will return `null` (e.g. unit tests)\n  // - A base path is stripped from the result\n  // - Rewrites *are* taken into account (i.e. the pathname\n  //   that the user sees in the browser is returned)\n  const pathname = usePathname();\n  const locale = useLocale();\n  return useMemo(() => {\n    if (!pathname) return pathname;\n    let unlocalizedPathname = pathname;\n    const prefix = getLocalePrefix(locale, config.localePrefix);\n    const isPathnamePrefixed = hasPathnamePrefixed(prefix, pathname);\n    if (isPathnamePrefixed) {\n      unlocalizedPathname = unprefixPathname(pathname, prefix);\n    } else if (config.localePrefix.mode === 'as-needed' && config.localePrefix.prefixes) {\n      // Workaround for https://github.com/vercel/next.js/issues/73085\n      const localeAsPrefix = getLocaleAsPrefix(locale);\n      if (hasPathnamePrefixed(localeAsPrefix, pathname)) {\n        unlocalizedPathname = unprefixPathname(pathname, localeAsPrefix);\n      }\n    }\n    return unlocalizedPathname;\n  }, [config.localePrefix, locale, pathname]);\n}\n\nexport { useBasePathname as default };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AAEA,SAAS,gBAAgB,MAAM;IAC7B,6DAA6D;IAC7D,4DAA4D;IAE5D,8BAA8B;IAC9B,0DAA0D;IAC1D,8CAA8C;IAC9C,4CAA4C;IAC5C,yDAAyD;IACzD,mDAAmD;IACnD,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,SAAS,CAAA,GAAA,kKAAA,CAAA,YAAS,AAAD;IACvB,OAAO,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACb,IAAI,CAAC,UAAU,OAAO;QACtB,IAAI,sBAAsB;QAC1B,MAAM,SAAS,CAAA,GAAA,6KAAA,CAAA,kBAAe,AAAD,EAAE,QAAQ,OAAO,YAAY;QAC1D,MAAM,qBAAqB,CAAA,GAAA,6KAAA,CAAA,sBAAmB,AAAD,EAAE,QAAQ;QACvD,IAAI,oBAAoB;YACtB,sBAAsB,CAAA,GAAA,6KAAA,CAAA,mBAAgB,AAAD,EAAE,UAAU;QACnD,OAAO,IAAI,OAAO,YAAY,CAAC,IAAI,KAAK,eAAe,OAAO,YAAY,CAAC,QAAQ,EAAE;YACnF,gEAAgE;YAChE,MAAM,iBAAiB,CAAA,GAAA,6KAAA,CAAA,oBAAiB,AAAD,EAAE;YACzC,IAAI,CAAA,GAAA,6KAAA,CAAA,sBAAmB,AAAD,EAAE,gBAAgB,WAAW;gBACjD,sBAAsB,CAAA,GAAA,6KAAA,CAAA,mBAAgB,AAAD,EAAE,UAAU;YACnD;QACF;QACA,OAAO;IACT,GAAG;QAAC,OAAO,YAAY;QAAE;QAAQ;KAAS;AAC5C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1221, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/praith/node_modules/next-intl/dist/esm/development/navigation/react-client/createNavigation.js"], "sourcesContent": ["import { useRouter, usePathname } from 'next/navigation';\nimport { useMemo } from 'react';\nimport { useLocale } from 'use-intl';\nimport createSharedNavigationFns from '../shared/createSharedNavigationFns.js';\nimport syncLocaleCookie from '../shared/syncLocaleCookie.js';\nimport { getRoute } from '../shared/utils.js';\nimport useBasePathname from './useBasePathname.js';\n\nfunction createNavigation(routing) {\n  const {\n    Link,\n    config,\n    getPathname,\n    ...redirects\n  } = createSharedNavigationFns(useLocale, routing);\n\n  /** @see https://next-intl.dev/docs/routing/navigation#usepathname */\n  function usePathname$1() {\n    const pathname = useBasePathname(config);\n    const locale = useLocale();\n\n    // @ts-expect-error -- Mirror the behavior from Next.js, where `null` is returned when `usePathname` is used outside of Next, but the types indicate that a string is always returned.\n    return useMemo(() => pathname &&\n    // @ts-expect-error -- This is fine\n    config.pathnames ? getRoute(locale, pathname,\n    // @ts-expect-error -- This is fine\n    config.pathnames) : pathname, [locale, pathname]);\n  }\n  function useRouter$1() {\n    const router = useRouter();\n    const curLocale = useLocale();\n    const nextPathname = usePathname();\n    return useMemo(() => {\n      function createHandler(fn) {\n        return function handler(href, options) {\n          const {\n            locale: nextLocale,\n            ...rest\n          } = options || {};\n          const pathname = getPathname({\n            href,\n            locale: nextLocale || curLocale\n          });\n          const args = [pathname];\n          if (Object.keys(rest).length > 0) {\n            // @ts-expect-error -- This is fine\n            args.push(rest);\n          }\n          syncLocaleCookie(config.localeCookie, nextPathname, curLocale, nextLocale);\n          fn(...args);\n        };\n      }\n      return {\n        ...router,\n        /** @see https://next-intl.dev/docs/routing/navigation#userouter */\n        push: createHandler(router.push),\n        /** @see https://next-intl.dev/docs/routing/navigation#userouter */\n        replace: createHandler(router.replace),\n        /** @see https://next-intl.dev/docs/routing/navigation#userouter */\n        prefetch: createHandler(router.prefetch)\n      };\n    }, [curLocale, nextPathname, router]);\n  }\n  return {\n    ...redirects,\n    Link,\n    usePathname: usePathname$1,\n    useRouter: useRouter$1,\n    getPathname\n  };\n}\n\nexport { createNavigation as default };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AAEA,SAAS,iBAAiB,OAAO;IAC/B,MAAM,EACJ,IAAI,EACJ,MAAM,EACN,WAAW,EACX,GAAG,WACJ,GAAG,CAAA,GAAA,+MAAA,CAAA,UAAyB,AAAD,EAAE,kKAAA,CAAA,YAAS,EAAE;IAEzC,mEAAmE,GACnE,SAAS;QACP,MAAM,WAAW,CAAA,GAAA,8MAAA,CAAA,UAAe,AAAD,EAAE;QACjC,MAAM,SAAS,CAAA,GAAA,kKAAA,CAAA,YAAS,AAAD;QAEvB,sLAAsL;QACtL,OAAO,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM,YACrB,mCAAmC;YACnC,OAAO,SAAS,GAAG,CAAA,GAAA,2LAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ,UACpC,mCAAmC;YACnC,OAAO,SAAS,IAAI,UAAU;YAAC;YAAQ;SAAS;IAClD;IACA,SAAS;QACP,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;QACvB,MAAM,YAAY,CAAA,GAAA,kKAAA,CAAA,YAAS,AAAD;QAC1B,MAAM,eAAe,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;QAC/B,OAAO,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;YACb,SAAS,cAAc,EAAE;gBACvB,OAAO,SAAS,QAAQ,IAAI,EAAE,OAAO;oBACnC,MAAM,EACJ,QAAQ,UAAU,EAClB,GAAG,MACJ,GAAG,WAAW,CAAC;oBAChB,MAAM,WAAW,YAAY;wBAC3B;wBACA,QAAQ,cAAc;oBACxB;oBACA,MAAM,OAAO;wBAAC;qBAAS;oBACvB,IAAI,OAAO,IAAI,CAAC,MAAM,MAAM,GAAG,GAAG;wBAChC,mCAAmC;wBACnC,KAAK,IAAI,CAAC;oBACZ;oBACA,CAAA,GAAA,sMAAA,CAAA,UAAgB,AAAD,EAAE,OAAO,YAAY,EAAE,cAAc,WAAW;oBAC/D,MAAM;gBACR;YACF;YACA,OAAO;gBACL,GAAG,MAAM;gBACT,iEAAiE,GACjE,MAAM,cAAc,OAAO,IAAI;gBAC/B,iEAAiE,GACjE,SAAS,cAAc,OAAO,OAAO;gBACrC,iEAAiE,GACjE,UAAU,cAAc,OAAO,QAAQ;YACzC;QACF,GAAG;YAAC;YAAW;YAAc;SAAO;IACtC;IACA,OAAO;QACL,GAAG,SAAS;QACZ;QACA,aAAa;QACb,WAAW;QACX;IACF;AACF", "ignoreList": [0], "debugId": null}}]}