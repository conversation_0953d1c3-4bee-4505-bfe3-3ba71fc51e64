module.exports = {

"[project]/src/app/layout.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// Since we have a `not-found.tsx` page on the root, a layout file
// is required, even if it's just passing children through.
__turbopack_context__.s({
    "default": ()=>RootLayout
});
function RootLayout({ children }) {
    return children;
}
}),

};

//# sourceMappingURL=src_app_layout_tsx_55ed5bda._.js.map