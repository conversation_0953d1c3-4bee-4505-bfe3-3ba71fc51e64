'use client';

import { useTranslations } from 'next-intl';

export default function CryptoLogos() {
  const t = useTranslations('cryptocurrencies');

  const cryptos = [
    {
      name: t('tether'),
      symbol: 'USDT',
      icon: '₮',
      color: 'text-green-400'
    },
    {
      name: t('bitcoin'),
      symbol: 'BTC',
      icon: '₿',
      color: 'text-orange-400'
    },
    {
      name: t('tron'),
      symbol: 'TRX',
      icon: 'T',
      color: 'text-red-400'
    },
    {
      name: t('ethereum'),
      symbol: 'ETH',
      icon: 'Ξ',
      color: 'text-blue-400'
    },
    {
      name: t('litecoin'),
      symbol: 'LTC',
      icon: 'Ł',
      color: 'text-gray-400'
    }
  ];

  return (
    <div className="container mx-auto px-6">
      <div className="flex items-center justify-center space-x-12 lg:space-x-16">
        {cryptos.map((crypto, index) => (
          <div key={index} className="flex items-center space-x-3 opacity-70 hover:opacity-100 transition-opacity">
            <div className={`text-2xl font-bold ${crypto.color}`}>
              {crypto.icon}
            </div>
            <div className="text-white">
              <div className="font-semibold text-sm">{crypto.name}</div>
              <div className="text-xs text-gray-400">{crypto.symbol}</div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
