import Decimal from 'decimal.js';
import { RawNumberFormatResult, UnsignedRoundingModeType } from '../types/number';
/**
 * https://tc39.es/ecma402/#sec-torawprecision
 * @param x a finite non-negative Number or BigInt
 * @param minPrecision an integer between 1 and 21
 * @param maxPrecision an integer between 1 and 21
 */
export declare function ToRawPrecision(x: Decimal, minPrecision: number, maxPrecision: number, unsignedRoundingMode: UnsignedRoundingModeType): RawNumberFormatResult;
