'use client';

import { useTranslations } from 'next-intl';
import Navigation from './Navigation';
import CryptoLogos from './CryptoLogos';
import GlobalVisualization from './GlobalVisualization';

export default function HeroSection() {
  const t = useTranslations('hero');

  return (
    <div className="min-h-screen bg-black text-white relative overflow-hidden">
      {/* Navigation */}
      <Navigation />
      
      {/* Main Hero Content */}
      <div className="container mx-auto px-8 pt-8 pb-32 relative z-10">
        <div className="grid lg:grid-cols-2 gap-16 items-center">
          {/* Left Content */}
          <div className="space-y-8 max-w-xl">
            {/* Trust Badge */}
            <div className="flex items-center space-x-3">
              <div className="w-4 h-4 bg-orange-500 rounded-full flex items-center justify-center">
                <span className="text-white text-xs font-bold">✓</span>
              </div>
              <span className="text-orange-500 text-sm font-bold tracking-wider uppercase">
                100% TRUSTED PLATFORM
              </span>
            </div>

            {/* Main Heading */}
            <div className="space-y-4">
              <h1 className="text-5xl lg:text-6xl font-black leading-tight">
                POWERING GLOBAL<br />
                PAYMENTS WITH<br />
                <span className="text-[#7ED321]">BLOCKCHAIN</span>
              </h1>
            </div>

            {/* Description */}
            <p className="text-gray-300 text-lg leading-relaxed">
              Praith Is A Blockchain-Powered Crypto Payment Gateway That Solves The Traditional Payment Problems. Our Platform Enables Fast, Low-Fee, And Borderless Transactions Using Stablecoins On The Tron And Binance Smart Chain (BEP-20) Networks.
            </p>

            {/* CTA Button */}
            <div className="pt-4">
              <button className="bg-[#7ED321] text-black px-8 py-3 rounded-md font-bold text-base hover:bg-[#6BC91A] transition-all duration-300">
                Get Started
              </button>
            </div>
          </div>

          {/* Right Content - Global Visualization */}
          <div className="relative">
            <GlobalVisualization />
          </div>
        </div>
      </div>

      {/* Crypto Logos Footer */}
      <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-r from-gray-800 via-gray-700 to-gray-800 py-6">
        <CryptoLogos />
      </div>
    </div>
  );
}
