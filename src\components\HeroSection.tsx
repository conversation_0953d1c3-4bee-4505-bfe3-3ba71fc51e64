'use client';

import { useTranslations } from 'next-intl';
import Navigation from './Navigation';
import CryptoLogos from './CryptoLogos';
import GlobalVisualization from './GlobalVisualization';

export default function HeroSection() {
  const t = useTranslations('hero');

  return (
    <div className="min-h-screen bg-black text-white relative overflow-hidden">
      {/* Navigation */}
      <Navigation />
      
      {/* Main Hero Content */}
      <div className="container mx-auto px-6 py-16 relative z-10">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Left Content */}
          <div className="space-y-8">
            {/* Trust Badge */}
            <div className="flex items-center space-x-2 text-orange-400">
              <div className="w-4 h-4 bg-orange-400 rounded-full flex items-center justify-center">
                <span className="text-black text-xs font-bold">⚠</span>
              </div>
              <span className="text-sm font-medium tracking-wide">
                {t('trustedPlatform')}
              </span>
            </div>

            {/* Main Heading */}
            <div className="space-y-4">
              <h1 className="text-4xl lg:text-6xl font-bold leading-tight">
                {t('title')}
                <br />
                <span className="text-green-400">{t('titleHighlight')}</span>
              </h1>
            </div>

            {/* Description */}
            <p className="text-gray-300 text-lg leading-relaxed max-w-2xl">
              {t('description')}
            </p>

            {/* CTA Button */}
            <div className="pt-4">
              <button className="bg-green-400 text-black px-8 py-4 rounded-md font-semibold text-lg hover:bg-green-500 transition-colors">
                {t('getStarted')}
              </button>
            </div>
          </div>

          {/* Right Content - Global Visualization */}
          <div className="relative">
            <GlobalVisualization />
          </div>
        </div>
      </div>

      {/* Crypto Logos Footer */}
      <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-r from-gray-900 to-gray-800 py-6">
        <CryptoLogos />
      </div>
    </div>
  );
}
