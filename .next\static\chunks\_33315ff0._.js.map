{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/praith/src/i18n/routing.ts"], "sourcesContent": ["import {defineRouting} from 'next-intl/routing';\nimport {createNavigation} from 'next-intl/navigation';\n \nexport const routing = defineRouting({\n  // A list of all locales that are supported\n  locales: ['en', 'es', 'fr', 'de'],\n \n  // Used when no locale matches\n  defaultLocale: 'en',\n\n  // The `pathnames` object holds pairs of internal and\n  // external paths. Based on the locale, the external\n  // paths are rewritten to the shared, internal ones.\n  pathnames: {\n    // If all locales use the same pathname, a single\n    // external path can be provided for all locales\n    '/': '/',\n    '/about': {\n      en: '/about',\n      es: '/acerca-de',\n      fr: '/a-propos',\n      de: '/uber-uns'\n    }\n  }\n});\n \n// Lightweight wrappers around Next.js' navigation APIs\n// that will consider the routing configuration\nexport const {Link, redirect, usePathname, useRouter} =\n  createNavigation(routing);\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;;;AAEO,MAAM,UAAU,CAAA,GAAA,qOAAA,CAAA,gBAAa,AAAD,EAAE;IACnC,2CAA2C;IAC3C,SAAS;QAAC;QAAM;QAAM;QAAM;KAAK;IAEjC,8BAA8B;IAC9B,eAAe;IAEf,qDAAqD;IACrD,oDAAoD;IACpD,oDAAoD;IACpD,WAAW;QACT,iDAAiD;QACjD,gDAAgD;QAChD,KAAK;QACL,UAAU;YACR,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;IACF;AACF;AAIO,MAAM,EAAC,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAC,GACnD,CAAA,GAAA,iQAAA,CAAA,mBAAgB,AAAD,EAAE", "debugId": null}}, {"offset": {"line": 53, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/praith/src/components/Navigation.tsx"], "sourcesContent": ["'use client';\n\nimport { useTranslations } from 'next-intl';\nimport { Link } from '@/i18n/routing';\nimport Image from 'next/image';\n\nexport default function Navigation() {\n  const t = useTranslations('navigation');\n\n  return (\n    <nav className=\"flex items-center justify-between px-6 py-4 bg-black text-white\">\n      {/* Logo */}\n      <div className=\"flex items-center\">\n        <div className=\"text-2xl font-bold\">\n          <span className=\"text-green-400\">∞</span>\n          <span className=\"ml-1\">Praith</span>\n        </div>\n      </div>\n\n      {/* Navigation Links */}\n      <div className=\"hidden md:flex items-center space-x-8\">\n        <Link href=\"/\" className=\"hover:text-green-400 transition-colors\">\n          {t('home')}\n        </Link>\n        <Link href=\"/product\" className=\"hover:text-green-400 transition-colors\">\n          {t('productPage')}\n        </Link>\n        <Link href=\"/contact\" className=\"hover:text-green-400 transition-colors\">\n          {t('contactUs')}\n        </Link>\n      </div>\n\n      {/* Right side buttons */}\n      <div className=\"flex items-center space-x-4\">\n        <button className=\"bg-green-400 text-black px-6 py-2 rounded-md font-medium hover:bg-green-500 transition-colors\">\n          {t('getStarted')}\n        </button>\n        <div className=\"flex items-center space-x-2\">\n          <span className=\"text-sm\">{t('language')}</span>\n          <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 9l-7 7-7-7\" />\n          </svg>\n        </div>\n      </div>\n    </nav>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAMe,SAAS;;IACtB,MAAM,IAAI,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD,EAAE;IAE1B,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAK,WAAU;sCAAiB;;;;;;sCACjC,6LAAC;4BAAK,WAAU;sCAAO;;;;;;;;;;;;;;;;;0BAK3B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,yHAAA,CAAA,OAAI;wBAAC,MAAK;wBAAI,WAAU;kCACtB,EAAE;;;;;;kCAEL,6LAAC,yHAAA,CAAA,OAAI;wBAAC,MAAK;wBAAW,WAAU;kCAC7B,EAAE;;;;;;kCAEL,6LAAC,yHAAA,CAAA,OAAI;wBAAC,MAAK;wBAAW,WAAU;kCAC7B,EAAE;;;;;;;;;;;;0BAKP,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAO,WAAU;kCACf,EAAE;;;;;;kCAEL,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAU;0CAAW,EAAE;;;;;;0CAC7B,6LAAC;gCAAI,WAAU;gCAAU,MAAK;gCAAO,QAAO;gCAAe,SAAQ;0CACjE,cAAA,6LAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMjF;GAxCwB;;QACZ,yMAAA,CAAA,kBAAe;;;KADH", "debugId": null}}, {"offset": {"line": 216, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/praith/src/components/CryptoLogos.tsx"], "sourcesContent": ["'use client';\n\nimport { useTranslations } from 'next-intl';\n\nexport default function CryptoLogos() {\n  const t = useTranslations('cryptocurrencies');\n\n  const cryptos = [\n    {\n      name: t('tether'),\n      symbol: 'USDT',\n      icon: '₮',\n      color: 'text-green-400'\n    },\n    {\n      name: t('bitcoin'),\n      symbol: 'BTC',\n      icon: '₿',\n      color: 'text-orange-400'\n    },\n    {\n      name: t('tron'),\n      symbol: 'TRX',\n      icon: 'T',\n      color: 'text-red-400'\n    },\n    {\n      name: t('ethereum'),\n      symbol: 'ETH',\n      icon: 'Ξ',\n      color: 'text-blue-400'\n    },\n    {\n      name: t('litecoin'),\n      symbol: 'LTC',\n      icon: 'Ł',\n      color: 'text-gray-400'\n    }\n  ];\n\n  return (\n    <div className=\"container mx-auto px-6\">\n      <div className=\"flex items-center justify-center space-x-12 lg:space-x-16\">\n        {cryptos.map((crypto, index) => (\n          <div key={index} className=\"flex items-center space-x-3 opacity-70 hover:opacity-100 transition-opacity\">\n            <div className={`text-2xl font-bold ${crypto.color}`}>\n              {crypto.icon}\n            </div>\n            <div className=\"text-white\">\n              <div className=\"font-semibold text-sm\">{crypto.name}</div>\n              <div className=\"text-xs text-gray-400\">{crypto.symbol}</div>\n            </div>\n          </div>\n        ))}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAIe,SAAS;;IACtB,MAAM,IAAI,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD,EAAE;IAE1B,MAAM,UAAU;QACd;YACE,MAAM,EAAE;YACR,QAAQ;YACR,MAAM;YACN,OAAO;QACT;QACA;YACE,MAAM,EAAE;YACR,QAAQ;YACR,MAAM;YACN,OAAO;QACT;QACA;YACE,MAAM,EAAE;YACR,QAAQ;YACR,MAAM;YACN,OAAO;QACT;QACA;YACE,MAAM,EAAE;YACR,QAAQ;YACR,MAAM;YACN,OAAO;QACT;QACA;YACE,MAAM,EAAE;YACR,QAAQ;YACR,MAAM;YACN,OAAO;QACT;KACD;IAED,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAA<PERSON>,WAAU;sBACZ,QAAQ,GAAG,CAAC,CAAC,QAAQ,sBACpB,6LAAC;oBAAgB,WAAU;;sCACzB,6LAAC;4BAAI,WAAW,AAAC,sBAAkC,OAAb,OAAO,KAAK;sCAC/C,OAAO,IAAI;;;;;;sCAEd,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CAAyB,OAAO,IAAI;;;;;;8CACnD,6LAAC;oCAAI,WAAU;8CAAyB,OAAO,MAAM;;;;;;;;;;;;;mBAN/C;;;;;;;;;;;;;;;AAapB;GArDwB;;QACZ,yMAAA,CAAA,kBAAe;;;KADH", "debugId": null}}, {"offset": {"line": 334, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/praith/src/components/GlobalVisualization.tsx"], "sourcesContent": ["'use client';\n\nexport default function GlobalVisualization() {\n  return (\n    <div className=\"relative w-full h-96 lg:h-[500px]\">\n      {/* World Map Background (Dotted Pattern) */}\n      <div className=\"absolute inset-0 opacity-30\">\n        <svg\n          viewBox=\"0 0 800 500\"\n          className=\"w-full h-full\"\n          fill=\"none\"\n          xmlns=\"http://www.w3.org/2000/svg\"\n        >\n          {/* Dotted world map pattern */}\n          <defs>\n            <pattern id=\"dots\" x=\"0\" y=\"0\" width=\"20\" height=\"20\" patternUnits=\"userSpaceOnUse\">\n              <circle cx=\"10\" cy=\"10\" r=\"1\" fill=\"#374151\" />\n            </pattern>\n          </defs>\n          <rect width=\"800\" height=\"500\" fill=\"url(#dots)\" />\n          \n          {/* Simplified continent shapes with dots */}\n          <g fill=\"#4B5563\">\n            {/* North America */}\n            <circle cx=\"150\" cy=\"120\" r=\"2\" />\n            <circle cx=\"170\" cy=\"140\" r=\"2\" />\n            <circle cx=\"190\" cy=\"160\" r=\"2\" />\n            \n            {/* Europe */}\n            <circle cx=\"400\" cy=\"100\" r=\"2\" />\n            <circle cx=\"420\" cy=\"120\" r=\"2\" />\n            \n            {/* Asia */}\n            <circle cx=\"600\" cy=\"140\" r=\"2\" />\n            <circle cx=\"650\" cy=\"160\" r=\"2\" />\n            \n            {/* Africa */}\n            <circle cx=\"450\" cy=\"250\" r=\"2\" />\n            <circle cx=\"470\" cy=\"280\" r=\"2\" />\n            \n            {/* South America */}\n            <circle cx=\"250\" cy=\"300\" r=\"2\" />\n            <circle cx=\"270\" cy=\"350\" r=\"2\" />\n            \n            {/* Australia */}\n            <circle cx=\"700\" cy=\"350\" r=\"2\" />\n          </g>\n        </svg>\n      </div>\n\n      {/* Central Hub */}\n      <div className=\"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2\">\n        <div className=\"relative\">\n          {/* Central green circle with icons */}\n          <div className=\"w-20 h-20 bg-green-400 rounded-full flex items-center justify-center relative\">\n            <div className=\"w-8 h-8 bg-white rounded-sm flex items-center justify-center\">\n              <span className=\"text-green-600 font-bold text-lg\">₿</span>\n            </div>\n            <div className=\"absolute -top-2 -right-2 w-8 h-8 bg-green-500 rounded-full flex items-center justify-center\">\n              <span className=\"text-white text-xs\">⚡</span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Floating Crypto Icons */}\n      <div className=\"absolute top-16 left-16\">\n        <div className=\"w-12 h-12 bg-red-500 rounded-full flex items-center justify-center animate-pulse\">\n          <span className=\"text-white font-bold\">₿</span>\n        </div>\n      </div>\n\n      <div className=\"absolute top-32 right-20\">\n        <div className=\"w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center animate-pulse\">\n          <span className=\"text-white font-bold\">Ξ</span>\n        </div>\n      </div>\n\n      <div className=\"absolute bottom-32 left-20\">\n        <div className=\"w-12 h-12 bg-teal-500 rounded-full flex items-center justify-center animate-pulse\">\n          <span className=\"text-white font-bold\">T</span>\n        </div>\n      </div>\n\n      <div className=\"absolute bottom-20 right-32\">\n        <div className=\"w-12 h-12 bg-orange-500 rounded-full flex items-center justify-center animate-pulse\">\n          <span className=\"text-white font-bold\">Ł</span>\n        </div>\n      </div>\n\n      {/* Orbital Rings */}\n      <div className=\"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2\">\n        <div className=\"w-64 h-64 border border-green-400 rounded-full opacity-30 animate-spin\" style={{animationDuration: '20s'}}></div>\n        <div className=\"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-80 h-80 border border-green-400 rounded-full opacity-20 animate-spin\" style={{animationDuration: '30s', animationDirection: 'reverse'}}></div>\n      </div>\n\n      {/* Connection Lines */}\n      <svg className=\"absolute inset-0 w-full h-full pointer-events-none\">\n        <defs>\n          <linearGradient id=\"lineGradient\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"0%\">\n            <stop offset=\"0%\" stopColor=\"#10B981\" stopOpacity=\"0\" />\n            <stop offset=\"50%\" stopColor=\"#10B981\" stopOpacity=\"0.8\" />\n            <stop offset=\"100%\" stopColor=\"#10B981\" stopOpacity=\"0\" />\n          </linearGradient>\n        </defs>\n        \n        {/* Animated connection lines */}\n        <line x1=\"50%\" y1=\"50%\" x2=\"10%\" y2=\"20%\" stroke=\"url(#lineGradient)\" strokeWidth=\"2\" className=\"animate-pulse\" />\n        <line x1=\"50%\" y1=\"50%\" x2=\"90%\" y2=\"30%\" stroke=\"url(#lineGradient)\" strokeWidth=\"2\" className=\"animate-pulse\" />\n        <line x1=\"50%\" y1=\"50%\" x2=\"20%\" y2=\"80%\" stroke=\"url(#lineGradient)\" strokeWidth=\"2\" className=\"animate-pulse\" />\n        <line x1=\"50%\" y1=\"50%\" x2=\"80%\" y2=\"85%\" stroke=\"url(#lineGradient)\" strokeWidth=\"2\" className=\"animate-pulse\" />\n      </svg>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAEe,SAAS;IACtB,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBACC,SAAQ;oBACR,WAAU;oBACV,MAAK;oBACL,OAAM;;sCAGN,6LAAC;sCACC,cAAA,6LAAC;gCAAQ,IAAG;gCAAO,GAAE;gCAAI,GAAE;gCAAI,OAAM;gCAAK,QAAO;gCAAK,cAAa;0CACjE,cAAA,6LAAC;oCAAO,IAAG;oCAAK,IAAG;oCAAK,GAAE;oCAAI,MAAK;;;;;;;;;;;;;;;;sCAGvC,6LAAC;4BAAK,OAAM;4BAAM,QAAO;4BAAM,MAAK;;;;;;sCAGpC,6LAAC;4BAAE,MAAK;;8CAEN,6LAAC;oCAAO,IAAG;oCAAM,IAAG;oCAAM,GAAE;;;;;;8CAC5B,6LAAC;oCAAO,IAAG;oCAAM,IAAG;oCAAM,GAAE;;;;;;8CAC5B,6LAAC;oCAAO,IAAG;oCAAM,IAAG;oCAAM,GAAE;;;;;;8CAG5B,6LAAC;oCAAO,IAAG;oCAAM,IAAG;oCAAM,GAAE;;;;;;8CAC5B,6LAAC;oCAAO,IAAG;oCAAM,IAAG;oCAAM,GAAE;;;;;;8CAG5B,6LAAC;oCAAO,IAAG;oCAAM,IAAG;oCAAM,GAAE;;;;;;8CAC5B,6LAAC;oCAAO,IAAG;oCAAM,IAAG;oCAAM,GAAE;;;;;;8CAG5B,6LAAC;oCAAO,IAAG;oCAAM,IAAG;oCAAM,GAAE;;;;;;8CAC5B,6LAAC;oCAAO,IAAG;oCAAM,IAAG;oCAAM,GAAE;;;;;;8CAG5B,6LAAC;oCAAO,IAAG;oCAAM,IAAG;oCAAM,GAAE;;;;;;8CAC5B,6LAAC;oCAAO,IAAG;oCAAM,IAAG;oCAAM,GAAE;;;;;;8CAG5B,6LAAC;oCAAO,IAAG;oCAAM,IAAG;oCAAM,GAAE;;;;;;;;;;;;;;;;;;;;;;;0BAMlC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BAEb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAK,WAAU;8CAAmC;;;;;;;;;;;0CAErD,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAK,WAAU;8CAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO7C,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAK,WAAU;kCAAuB;;;;;;;;;;;;;;;;0BAI3C,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAK,WAAU;kCAAuB;;;;;;;;;;;;;;;;0BAI3C,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAK,WAAU;kCAAuB;;;;;;;;;;;;;;;;0BAI3C,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAK,WAAU;kCAAuB;;;;;;;;;;;;;;;;0BAK3C,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;wBAAyE,OAAO;4BAAC,mBAAmB;wBAAK;;;;;;kCACxH,6LAAC;wBAAI,WAAU;wBAA+I,OAAO;4BAAC,mBAAmB;4BAAO,oBAAoB;wBAAS;;;;;;;;;;;;0BAI/N,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;kCACC,cAAA,6LAAC;4BAAe,IAAG;4BAAe,IAAG;4BAAK,IAAG;4BAAK,IAAG;4BAAO,IAAG;;8CAC7D,6LAAC;oCAAK,QAAO;oCAAK,WAAU;oCAAU,aAAY;;;;;;8CAClD,6LAAC;oCAAK,QAAO;oCAAM,WAAU;oCAAU,aAAY;;;;;;8CACnD,6LAAC;oCAAK,QAAO;oCAAO,WAAU;oCAAU,aAAY;;;;;;;;;;;;;;;;;kCAKxD,6LAAC;wBAAK,IAAG;wBAAM,IAAG;wBAAM,IAAG;wBAAM,IAAG;wBAAM,QAAO;wBAAqB,aAAY;wBAAI,WAAU;;;;;;kCAChG,6LAAC;wBAAK,IAAG;wBAAM,IAAG;wBAAM,IAAG;wBAAM,IAAG;wBAAM,QAAO;wBAAqB,aAAY;wBAAI,WAAU;;;;;;kCAChG,6LAAC;wBAAK,IAAG;wBAAM,IAAG;wBAAM,IAAG;wBAAM,IAAG;wBAAM,QAAO;wBAAqB,aAAY;wBAAI,WAAU;;;;;;kCAChG,6LAAC;wBAAK,IAAG;wBAAM,IAAG;wBAAM,IAAG;wBAAM,IAAG;wBAAM,QAAO;wBAAqB,aAAY;wBAAI,WAAU;;;;;;;;;;;;;;;;;;AAIxG;KAhHwB", "debugId": null}}, {"offset": {"line": 814, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/praith/src/components/HeroSection.tsx"], "sourcesContent": ["'use client';\n\nimport { useTranslations } from 'next-intl';\nimport Navigation from './Navigation';\nimport CryptoLogos from './CryptoLogos';\nimport GlobalVisualization from './GlobalVisualization';\n\nexport default function HeroSection() {\n  const t = useTranslations('hero');\n\n  return (\n    <div className=\"min-h-screen bg-black text-white relative overflow-hidden\">\n      {/* Navigation */}\n      <Navigation />\n      \n      {/* Main Hero Content */}\n      <div className=\"container mx-auto px-6 py-16 relative z-10\">\n        <div className=\"grid lg:grid-cols-2 gap-12 items-center\">\n          {/* Left Content */}\n          <div className=\"space-y-8\">\n            {/* Trust Badge */}\n            <div className=\"flex items-center space-x-2 text-orange-400\">\n              <div className=\"w-4 h-4 bg-orange-400 rounded-full flex items-center justify-center\">\n                <span className=\"text-black text-xs font-bold\">⚠</span>\n              </div>\n              <span className=\"text-sm font-medium tracking-wide\">\n                {t('trustedPlatform')}\n              </span>\n            </div>\n\n            {/* Main Heading */}\n            <div className=\"space-y-4\">\n              <h1 className=\"text-4xl lg:text-6xl font-bold leading-tight\">\n                {t('title')}\n                <br />\n                <span className=\"text-green-400\">{t('titleHighlight')}</span>\n              </h1>\n            </div>\n\n            {/* Description */}\n            <p className=\"text-gray-300 text-lg leading-relaxed max-w-2xl\">\n              {t('description')}\n            </p>\n\n            {/* CTA Button */}\n            <div className=\"pt-4\">\n              <button className=\"bg-green-400 text-black px-8 py-4 rounded-md font-semibold text-lg hover:bg-green-500 transition-colors\">\n                {t('getStarted')}\n              </button>\n            </div>\n          </div>\n\n          {/* Right Content - Global Visualization */}\n          <div className=\"relative\">\n            <GlobalVisualization />\n          </div>\n        </div>\n      </div>\n\n      {/* Crypto Logos Footer */}\n      <div className=\"absolute bottom-0 left-0 right-0 bg-gradient-to-r from-gray-900 to-gray-800 py-6\">\n        <CryptoLogos />\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAOe,SAAS;;IACtB,MAAM,IAAI,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD,EAAE;IAE1B,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC,mIAAA,CAAA,UAAU;;;;;0BAGX,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;0DAA+B;;;;;;;;;;;sDAEjD,6LAAC;4CAAK,WAAU;sDACb,EAAE;;;;;;;;;;;;8CAKP,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAG,WAAU;;4CACX,EAAE;0DACH,6LAAC;;;;;0DACD,6LAAC;gDAAK,WAAU;0DAAkB,EAAE;;;;;;;;;;;;;;;;;8CAKxC,6LAAC;oCAAE,WAAU;8CACV,EAAE;;;;;;8CAIL,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAO,WAAU;kDACf,EAAE;;;;;;;;;;;;;;;;;sCAMT,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,4IAAA,CAAA,UAAmB;;;;;;;;;;;;;;;;;;;;;0BAM1B,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,oIAAA,CAAA,UAAW;;;;;;;;;;;;;;;;AAIpB;GA1DwB;;QACZ,yMAAA,CAAA,kBAAe;;;KADH", "debugId": null}}, {"offset": {"line": 998, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/praith/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js"], "sourcesContent": ["/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return type.displayName || \"Context\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = require(\"next/dist/compiled/react\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\"),\n      REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      react_stack_bottom_frame: function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React.react_stack_bottom_frame.bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC,GAGgB;AADjB;AACA,oEACE,AAAC;IACC,SAAS,yBAAyB,IAAI;QACpC,IAAI,QAAQ,MAAM,OAAO;QACzB,IAAI,eAAe,OAAO,MACxB,OAAO,KAAK,QAAQ,KAAK,yBACrB,OACA,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI;QACvC,IAAI,aAAa,OAAO,MAAM,OAAO;QACrC,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;QACX;QACA,IAAI,aAAa,OAAO,MACtB,OACG,aAAa,OAAO,KAAK,GAAG,IAC3B,QAAQ,KAAK,CACX,sHAEJ,KAAK,QAAQ;YAEb,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO,KAAK,WAAW,IAAI;YAC7B,KAAK;gBACH,OAAO,CAAC,KAAK,QAAQ,CAAC,WAAW,IAAI,SAAS,IAAI;YACpD,KAAK;gBACH,IAAI,YAAY,KAAK,MAAM;gBAC3B,OAAO,KAAK,WAAW;gBACvB,QACE,CAAC,AAAC,OAAO,UAAU,WAAW,IAAI,UAAU,IAAI,IAAI,IACnD,OAAO,OAAO,OAAO,gBAAgB,OAAO,MAAM,YAAa;gBAClE,OAAO;YACT,KAAK;gBACH,OACE,AAAC,YAAY,KAAK,WAAW,IAAI,MACjC,SAAS,YACL,YACA,yBAAyB,KAAK,IAAI,KAAK;YAE/C,KAAK;gBACH,YAAY,KAAK,QAAQ;gBACzB,OAAO,KAAK,KAAK;gBACjB,IAAI;oBACF,OAAO,yBAAyB,KAAK;gBACvC,EAAE,OAAO,GAAG,CAAC;QACjB;QACF,OAAO;IACT;IACA,SAAS,mBAAmB,KAAK;QAC/B,OAAO,KAAK;IACd;IACA,SAAS,uBAAuB,KAAK;QACnC,IAAI;YACF,mBAAmB;YACnB,IAAI,2BAA2B,CAAC;QAClC,EAAE,OAAO,GAAG;YACV,2BAA2B,CAAC;QAC9B;QACA,IAAI,0BAA0B;YAC5B,2BAA2B;YAC3B,IAAI,wBAAwB,yBAAyB,KAAK;YAC1D,IAAI,oCACF,AAAC,eAAe,OAAO,UACrB,OAAO,WAAW,IAClB,KAAK,CAAC,OAAO,WAAW,CAAC,IAC3B,MAAM,WAAW,CAAC,IAAI,IACtB;YACF,sBAAsB,IAAI,CACxB,0BACA,4GACA;YAEF,OAAO,mBAAmB;QAC5B;IACF;IACA,SAAS,YAAY,IAAI;QACvB,IAAI,SAAS,qBAAqB,OAAO;QACzC,IACE,aAAa,OAAO,QACpB,SAAS,QACT,KAAK,QAAQ,KAAK,iBAElB,OAAO;QACT,IAAI;YACF,IAAI,OAAO,yBAAyB;YACpC,OAAO,OAAO,MAAM,OAAO,MAAM;QACnC,EAAE,OAAO,GAAG;YACV,OAAO;QACT;IACF;IACA,SAAS;QACP,IAAI,aAAa,qBAAqB,CAAC;QACvC,OAAO,SAAS,aAAa,OAAO,WAAW,QAAQ;IACzD;IACA,SAAS;QACP,OAAO,MAAM;IACf;IACA,SAAS,YAAY,MAAM;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,IAAI,SAAS,OAAO,wBAAwB,CAAC,QAAQ,OAAO,GAAG;YAC/D,IAAI,UAAU,OAAO,cAAc,EAAE,OAAO,CAAC;QAC/C;QACA,OAAO,KAAK,MAAM,OAAO,GAAG;IAC9B;IACA,SAAS,2BAA2B,KAAK,EAAE,WAAW;QACpD,SAAS;YACP,8BACE,CAAC,AAAC,6BAA6B,CAAC,GAChC,QAAQ,KAAK,CACX,2OACA,YACD;QACL;QACA,sBAAsB,cAAc,GAAG,CAAC;QACxC,OAAO,cAAc,CAAC,OAAO,OAAO;YAClC,KAAK;YACL,cAAc,CAAC;QACjB;IACF;IACA,SAAS;QACP,IAAI,gBAAgB,yBAAyB,IAAI,CAAC,IAAI;QACtD,sBAAsB,CAAC,cAAc,IACnC,CAAC,AAAC,sBAAsB,CAAC,cAAc,GAAG,CAAC,GAC3C,QAAQ,KAAK,CACX,8IACD;QACH,gBAAgB,IAAI,CAAC,KAAK,CAAC,GAAG;QAC9B,OAAO,KAAK,MAAM,gBAAgB,gBAAgB;IACpD;IACA,SAAS,aACP,IAAI,EACJ,GAAG,EACH,IAAI,EACJ,MAAM,EACN,KAAK,EACL,KAAK,EACL,UAAU,EACV,SAAS;QAET,OAAO,MAAM,GAAG;QAChB,OAAO;YACL,UAAU;YACV,MAAM;YACN,KAAK;YACL,OAAO;YACP,QAAQ;QACV;QACA,SAAS,CAAC,KAAK,MAAM,OAAO,OAAO,IAAI,IACnC,OAAO,cAAc,CAAC,MAAM,OAAO;YACjC,YAAY,CAAC;YACb,KAAK;QACP,KACA,OAAO,cAAc,CAAC,MAAM,OAAO;YAAE,YAAY,CAAC;YAAG,OAAO;QAAK;QACrE,KAAK,MAAM,GAAG,CAAC;QACf,OAAO,cAAc,CAAC,KAAK,MAAM,EAAE,aAAa;YAC9C,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,eAAe;YACzC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,MAAM,IAAI,CAAC,OAAO,MAAM,CAAC,KAAK,KAAK,GAAG,OAAO,MAAM,CAAC,KAAK;QAChE,OAAO;IACT;IACA,SAAS,WACP,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,MAAM,EACN,IAAI,EACJ,UAAU,EACV,SAAS;QAET,IAAI,WAAW,OAAO,QAAQ;QAC9B,IAAI,KAAK,MAAM,UACb,IAAI,kBACF,IAAI,YAAY,WAAW;YACzB,IACE,mBAAmB,GACnB,mBAAmB,SAAS,MAAM,EAClC,mBAEA,kBAAkB,QAAQ,CAAC,iBAAiB;YAC9C,OAAO,MAAM,IAAI,OAAO,MAAM,CAAC;QACjC,OACE,QAAQ,KAAK,CACX;aAED,kBAAkB;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,WAAW,yBAAyB;YACpC,IAAI,OAAO,OAAO,IAAI,CAAC,QAAQ,MAAM,CAAC,SAAU,CAAC;gBAC/C,OAAO,UAAU;YACnB;YACA,mBACE,IAAI,KAAK,MAAM,GACX,oBAAoB,KAAK,IAAI,CAAC,aAAa,WAC3C;YACN,qBAAqB,CAAC,WAAW,iBAAiB,IAChD,CAAC,AAAC,OACA,IAAI,KAAK,MAAM,GAAG,MAAM,KAAK,IAAI,CAAC,aAAa,WAAW,MAC5D,QAAQ,KAAK,CACX,mOACA,kBACA,UACA,MACA,WAED,qBAAqB,CAAC,WAAW,iBAAiB,GAAG,CAAC,CAAE;QAC7D;QACA,WAAW;QACX,KAAK,MAAM,YACT,CAAC,uBAAuB,WAAY,WAAW,KAAK,QAAS;QAC/D,YAAY,WACV,CAAC,uBAAuB,OAAO,GAAG,GAAI,WAAW,KAAK,OAAO,GAAG,AAAC;QACnE,IAAI,SAAS,QAAQ;YACnB,WAAW,CAAC;YACZ,IAAK,IAAI,YAAY,OACnB,UAAU,YAAY,CAAC,QAAQ,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS;QAChE,OAAO,WAAW;QAClB,YACE,2BACE,UACA,eAAe,OAAO,OAClB,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI,YACjC;QAER,OAAO,aACL,MACA,UACA,MACA,QACA,YACA,UACA,YACA;IAEJ;IACA,SAAS,kBAAkB,IAAI;QAC7B,aAAa,OAAO,QAClB,SAAS,QACT,KAAK,QAAQ,KAAK,sBAClB,KAAK,MAAM,IACX,CAAC,KAAK,MAAM,CAAC,SAAS,GAAG,CAAC;IAC9B;IACA,IAAI,uHACF,qBAAqB,OAAO,GAAG,CAAC,+BAChC,oBAAoB,OAAO,GAAG,CAAC,iBAC/B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC,mBACjC,sBAAsB,OAAO,GAAG,CAAC,mBACjC,qBAAqB,OAAO,GAAG,CAAC,kBAChC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC,mBACjC,2BAA2B,OAAO,GAAG,CAAC,wBACtC,kBAAkB,OAAO,GAAG,CAAC,eAC7B,kBAAkB,OAAO,GAAG,CAAC,eAC7B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,2BACpC,uBACE,MAAM,+DAA+D,EACvE,iBAAiB,OAAO,SAAS,CAAC,cAAc,EAChD,cAAc,MAAM,OAAO,EAC3B,aAAa,QAAQ,UAAU,GAC3B,QAAQ,UAAU,GAClB;QACE,OAAO;IACT;IACN,QAAQ;QACN,0BAA0B,SAAU,iBAAiB;YACnD,OAAO;QACT;IACF;IACA,IAAI;IACJ,IAAI,yBAAyB,CAAC;IAC9B,IAAI,yBAAyB,MAAM,wBAAwB,CAAC,IAAI,CAC9D,OACA;IAEF,IAAI,wBAAwB,WAAW,YAAY;IACnD,IAAI,wBAAwB,CAAC;IAC7B,QAAQ,QAAQ,GAAG;IACnB,QAAQ,MAAM,GAAG,SACf,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,MAAM,EACN,IAAI;QAEJ,IAAI,mBACF,MAAM,qBAAqB,0BAA0B;QACvD,OAAO,WACL,MACA,QACA,UACA,kBACA,QACA,MACA,mBACI,MAAM,2BACN,wBACJ,mBAAmB,WAAW,YAAY,SAAS;IAEvD;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1205, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/praith/node_modules/next/dist/compiled/react/jsx-dev-runtime.js"], "sourcesContent": ["'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-jsx-dev-runtime.production.js');\n} else {\n  module.exports = require('./cjs/react-jsx-dev-runtime.development.js');\n}\n"], "names": [], "mappings": "AAEI;AAFJ;AAEA;;KAEO;IACL,OAAO,OAAO;AAChB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1216, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/praith/node_modules/next-intl/dist/esm/development/react-client/index.js"], "sourcesContent": ["import { useFormatter as useFormatter$1, useTranslations as useTranslations$1 } from 'use-intl';\nexport * from 'use-intl';\n\n/**\n * This is the main entry file when non-'react-server'\n * environments import from 'next-intl'.\n *\n * Maintainer notes:\n * - Make sure this mirrors the API from 'react-server'.\n * - Make sure everything exported from this module is\n *   supported in all Next.js versions that are supported.\n */\n\n\n// eslint-disable-next-line @typescript-eslint/no-unsafe-function-type\nfunction callHook(name, hook) {\n  return (...args) => {\n    try {\n      return hook(...args);\n    } catch {\n      throw new Error(`Failed to call \\`${name}\\` because the context from \\`NextIntlClientProvider\\` was not found.\n\nThis can happen because:\n1) You intended to render this component as a Server Component, the render\n   failed, and therefore <PERSON><PERSON> attempted to render the component on the client\n   instead. If this is the case, check the console for server errors.\n2) You intended to render this component on the client side, but no context was found.\n   Learn more about this error here: https://next-intl.dev/docs/environments/server-client-components#missing-context` );\n    }\n  };\n}\nconst useTranslations = callHook('useTranslations', useTranslations$1);\nconst useFormatter = callHook('useFormatter', useFormatter$1);\n\nexport { useFormatter, useTranslations };\n"], "names": [], "mappings": ";;;;AAAA;;;AAGA;;;;;;;;CAQC,GAGD,sEAAsE;AACtE,SAAS,SAAS,IAAI,EAAE,IAAI;IAC1B,OAAO;yCAAI;YAAA;;QACT,IAAI;YACF,OAAO,QAAQ;QACjB,EAAE,UAAM;YACN,MAAM,IAAI,MAAM,AAAC,mBAAwB,OAAL,MAAK;QAQ3C;IACF;AACF;AACA,MAAM,kBAAkB,SAAS,mBAAmB,qKAAA,CAAA,kBAAiB;AACrE,MAAM,eAAe,SAAS,gBAAgB,qKAAA,CAAA,eAAc", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1251, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/praith/node_modules/next-intl/dist/esm/development/routing/defineRouting.js"], "sourcesContent": ["function defineRouting(config) {\n  if (config.domains) {\n    validateUniqueLocalesPerDomain(config.domains);\n  }\n  return config;\n}\nfunction validateUniqueLocalesPerDomain(domains) {\n  const domainsByLocale = new Map();\n  for (const {\n    domain,\n    locales\n  } of domains) {\n    for (const locale of locales) {\n      const localeDomains = domainsByLocale.get(locale) || new Set();\n      localeDomains.add(domain);\n      domainsByLocale.set(locale, localeDomains);\n    }\n  }\n  const duplicateLocaleMessages = Array.from(domainsByLocale.entries()).filter(([, localeDomains]) => localeDomains.size > 1).map(([locale, localeDomains]) => `- \"${locale}\" is used by: ${Array.from(localeDomains).join(', ')}`);\n  if (duplicateLocaleMessages.length > 0) {\n    console.warn('Locales are expected to be unique per domain, but found overlap:\\n' + duplicateLocaleMessages.join('\\n') + '\\nPlease see https://next-intl.dev/docs/routing#domains');\n  }\n}\n\nexport { defineRouting as default };\n"], "names": [], "mappings": ";;;AAAA,SAAS,cAAc,MAAM;IAC3B,IAAI,OAAO,OAAO,EAAE;QAClB,+BAA+B,OAAO,OAAO;IAC/C;IACA,OAAO;AACT;AACA,SAAS,+BAA+B,OAAO;IAC7C,MAAM,kBAAkB,IAAI;IAC5B,KAAK,MAAM,EACT,MAAM,EACN,OAAO,EACR,IAAI,QAAS;QACZ,KAAK,MAAM,UAAU,QAAS;YAC5B,MAAM,gBAAgB,gBAAgB,GAAG,CAAC,WAAW,IAAI;YACzD,cAAc,GAAG,CAAC;YAClB,gBAAgB,GAAG,CAAC,QAAQ;QAC9B;IACF;IACA,MAAM,0BAA0B,MAAM,IAAI,CAAC,gBAAgB,OAAO,IAAI,MAAM,CAAC;YAAC,GAAG,cAAc;eAAK,cAAc,IAAI,GAAG;OAAG,GAAG,CAAC;YAAC,CAAC,QAAQ,cAAc;eAAK,AAAC,MAA4B,OAAvB,QAAO,kBAAqD,OAArC,MAAM,IAAI,CAAC,eAAe,IAAI,CAAC;;IACzN,IAAI,wBAAwB,MAAM,GAAG,GAAG;QACtC,QAAQ,IAAI,CAAC,uEAAuE,wBAAwB,IAAI,CAAC,QAAQ;IAC3H;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1293, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/praith/node_modules/next-intl/dist/esm/development/routing/config.js"], "sourcesContent": ["function receiveRoutingConfig(input) {\n  return {\n    ...input,\n    localePrefix: receiveLocalePrefixConfig(input.localePrefix),\n    localeCookie: receiveLocaleCookie(input.localeCookie),\n    localeDetection: input.localeDetection ?? true,\n    alternateLinks: input.alternateLinks ?? true\n  };\n}\nfunction receiveLocaleCookie(localeCookie) {\n  return localeCookie ?? true ? {\n    name: 'NEXT_LOCALE',\n    sameSite: 'lax',\n    ...(typeof localeCookie === 'object' && localeCookie)\n\n    // `path` needs to be provided based on a detected base path\n    // that depends on the environment when setting a cookie\n  } : false;\n}\nfunction receiveLocalePrefixConfig(localePrefix) {\n  return typeof localePrefix === 'object' ? localePrefix : {\n    mode: localePrefix || 'always'\n  };\n}\n\nexport { receiveRoutingConfig };\n"], "names": [], "mappings": ";;;AAAA,SAAS,qBAAqB,KAAK;QAKd,wBACD;IALlB,OAAO;QACL,GAAG,KAAK;QACR,cAAc,0BAA0B,MAAM,YAAY;QAC1D,cAAc,oBAAoB,MAAM,YAAY;QACpD,iBAAiB,CAAA,yBAAA,MAAM,eAAe,cAArB,oCAAA,yBAAyB;QAC1C,gBAAgB,CAAA,wBAAA,MAAM,cAAc,cAApB,mCAAA,wBAAwB;IAC1C;AACF;AACA,SAAS,oBAAoB,YAAY;IACvC,OAAO,CAAA,yBAAA,0BAAA,eAAgB,IAAG,IAAI;QAC5B,MAAM;QACN,UAAU;QACV,GAAI,OAAO,iBAAiB,YAAY,YAAY;IAItD,IAAI;AACN;AACA,SAAS,0BAA0B,YAAY;IAC7C,OAAO,OAAO,iBAAiB,WAAW,eAAe;QACvD,MAAM,gBAAgB;IACxB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1323, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/praith/node_modules/next-intl/dist/esm/development/shared/use.js"], "sourcesContent": ["import * as react from 'react';\n\n// @ts-expect-error -- Ooof, Next.js doesn't make this easy.\n// `use` is only available in React 19 canary, but we can\n// use it in Next.js already as Next.js \"vendors\" a fixed\n// version of React. However, if we'd simply put `use` in\n// ESM code, then the build doesn't work since React does\n// not export `use` officially. Therefore, we have to use\n// something that is not statically analyzable. Once React\n// 19 is out, we can remove this in the next major version.\nvar use = react['use'.trim()];\n\nexport { use as default };\n"], "names": [], "mappings": ";;;AAAA;;AAEA,4DAA4D;AAC5D,yDAAyD;AACzD,yDAAyD;AACzD,yDAAyD;AACzD,yDAAyD;AACzD,yDAAyD;AACzD,0DAA0D;AAC1D,2DAA2D;AAC3D,IAAI,MAAM,6JAAK,CAAC,MAAM,IAAI,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1342, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/praith/node_modules/next-intl/dist/esm/development/navigation/shared/createSharedNavigationFns.js"], "sourcesContent": ["import { redirect, permanentRedirect } from 'next/navigation';\nimport { forwardRef } from 'react';\nimport { receiveRoutingConfig } from '../../routing/config.js';\nimport use from '../../shared/use.js';\nimport { isLocalizableHref, isPromise } from '../../shared/utils.js';\nimport BaseLink from './BaseLink.js';\nimport { validateReceivedConfig, serializeSearchParams, compileLocalizedPathname, applyPathnamePrefix, normalizeNameOrNameWithParams } from './utils.js';\nimport { jsx } from 'react/jsx-runtime';\n\n/**\n * Shared implementations for `react-server` and `react-client`\n */\nfunction createSharedNavigationFns(getLocale, routing) {\n  const config = receiveRoutingConfig(routing || {});\n  {\n    validateReceivedConfig(config);\n  }\n  const pathnames = config.pathnames;\n  function Link({\n    href,\n    locale,\n    ...rest\n  }, ref) {\n    let pathname, params;\n    if (typeof href === 'object') {\n      pathname = href.pathname;\n      // @ts-expect-error -- This is ok\n      params = href.params;\n    } else {\n      pathname = href;\n    }\n\n    // @ts-expect-error -- This is ok\n    const isLocalizable = isLocalizableHref(href);\n    const localePromiseOrValue = getLocale();\n    const curLocale = isPromise(localePromiseOrValue) ? use(localePromiseOrValue) : localePromiseOrValue;\n    const finalPathname = isLocalizable ? getPathname({\n      locale: locale || curLocale,\n      // @ts-expect-error -- This is ok\n      href: pathnames == null ? pathname : {\n        pathname,\n        params\n      },\n      // Always include a prefix when changing locales\n      forcePrefix: locale != null || undefined\n    }) : pathname;\n    return /*#__PURE__*/jsx(BaseLink, {\n      ref: ref\n      // @ts-expect-error -- This is ok\n      ,\n      href: typeof href === 'object' ? {\n        ...href,\n        pathname: finalPathname\n      } : finalPathname,\n      locale: locale,\n      localeCookie: config.localeCookie,\n      ...rest\n    });\n  }\n  const LinkWithRef = /*#__PURE__*/forwardRef(Link);\n  function getPathname(args) {\n    const {\n      forcePrefix,\n      href,\n      locale\n    } = args;\n    let pathname;\n    if (pathnames == null) {\n      if (typeof href === 'object') {\n        pathname = href.pathname;\n        if (href.query) {\n          pathname += serializeSearchParams(href.query);\n        }\n      } else {\n        pathname = href;\n      }\n    } else {\n      pathname = compileLocalizedPathname({\n        locale,\n        // @ts-expect-error -- This is ok\n        ...normalizeNameOrNameWithParams(href),\n        // @ts-expect-error -- This is ok\n        pathnames: config.pathnames\n      });\n    }\n    return applyPathnamePrefix(pathname, locale, config, forcePrefix);\n  }\n  function getRedirectFn(fn) {\n    /** @see https://next-intl.dev/docs/routing/navigation#redirect */\n    return function redirectFn(args, ...rest) {\n      return fn(getPathname(args), ...rest);\n    };\n  }\n  const redirect$1 = getRedirectFn(redirect);\n  const permanentRedirect$1 = getRedirectFn(permanentRedirect);\n  return {\n    config,\n    Link: LinkWithRef,\n    redirect: redirect$1,\n    permanentRedirect: permanentRedirect$1,\n    getPathname\n  };\n}\n\nexport { createSharedNavigationFns as default };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;AAEA;;CAEC,GACD,SAAS,0BAA0B,SAAS,EAAE,OAAO;IACnD,MAAM,SAAS,CAAA,GAAA,kLAAA,CAAA,uBAAoB,AAAD,EAAE,WAAW,CAAC;IAChD;QACE,CAAA,GAAA,8LAAA,CAAA,yBAAsB,AAAD,EAAE;IACzB;IACA,MAAM,YAAY,OAAO,SAAS;IAClC,SAAS,KAAK,KAIb,EAAE,GAAG;YAJQ,EACZ,IAAI,EACJ,MAAM,EACN,GAAG,MACJ,GAJa;QAKZ,IAAI,UAAU;QACd,IAAI,OAAO,SAAS,UAAU;YAC5B,WAAW,KAAK,QAAQ;YACxB,iCAAiC;YACjC,SAAS,KAAK,MAAM;QACtB,OAAO;YACL,WAAW;QACb;QAEA,iCAAiC;QACjC,MAAM,gBAAgB,CAAA,GAAA,gLAAA,CAAA,oBAAiB,AAAD,EAAE;QACxC,MAAM,uBAAuB;QAC7B,MAAM,YAAY,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,wBAAwB,CAAA,GAAA,8KAAA,CAAA,UAAG,AAAD,EAAE,wBAAwB;QAChF,MAAM,gBAAgB,gBAAgB,YAAY;YAChD,QAAQ,UAAU;YAClB,iCAAiC;YACjC,MAAM,aAAa,OAAO,WAAW;gBACnC;gBACA;YACF;YACA,gDAAgD;YAChD,aAAa,UAAU,QAAQ;QACjC,KAAK;QACL,OAAO,WAAW,GAAE,CAAA,GAAA,sKAAA,CAAA,MAAG,AAAD,EAAE,iMAAA,CAAA,UAAQ,EAAE;YAChC,KAAK;YAGL,MAAM,OAAO,SAAS,WAAW;gBAC/B,GAAG,IAAI;gBACP,UAAU;YACZ,IAAI;YACJ,QAAQ;YACR,cAAc,OAAO,YAAY;YACjC,GAAG,IAAI;QACT;IACF;IACA,MAAM,cAAc,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC5C,SAAS,YAAY,IAAI;QACvB,MAAM,EACJ,WAAW,EACX,IAAI,EACJ,MAAM,EACP,GAAG;QACJ,IAAI;QACJ,IAAI,aAAa,MAAM;YACrB,IAAI,OAAO,SAAS,UAAU;gBAC5B,WAAW,KAAK,QAAQ;gBACxB,IAAI,KAAK,KAAK,EAAE;oBACd,YAAY,CAAA,GAAA,8LAAA,CAAA,wBAAqB,AAAD,EAAE,KAAK,KAAK;gBAC9C;YACF,OAAO;gBACL,WAAW;YACb;QACF,OAAO;YACL,WAAW,CAAA,GAAA,8LAAA,CAAA,2BAAwB,AAAD,EAAE;gBAClC;gBACA,iCAAiC;gBACjC,GAAG,CAAA,GAAA,8LAAA,CAAA,gCAA6B,AAAD,EAAE,KAAK;gBACtC,iCAAiC;gBACjC,WAAW,OAAO,SAAS;YAC7B;QACF;QACA,OAAO,CAAA,GAAA,8LAAA,CAAA,sBAAmB,AAAD,EAAE,UAAU,QAAQ,QAAQ;IACvD;IACA,SAAS,cAAc,EAAE;QACvB,gEAAgE,GAChE,OAAO,SAAS,WAAW,IAAI;YAAE,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,OAAH,UAAA,OAAA,IAAA,OAAA,QAAA,OAAA,GAAA,OAAA,MAAA;gBAAG,KAAH,OAAA,KAAA,SAAA,CAAA,KAAO;;YACtC,OAAO,GAAG,YAAY,UAAU;QAClC;IACF;IACA,MAAM,aAAa,cAAc,qIAAA,CAAA,WAAQ;IACzC,MAAM,sBAAsB,cAAc,qIAAA,CAAA,oBAAiB;IAC3D,OAAO;QACL;QACA,MAAM;QACN,UAAU;QACV,mBAAmB;QACnB;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1451, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/praith/node_modules/next-intl/dist/esm/development/navigation/react-client/useBasePathname.js"], "sourcesContent": ["import { usePathname } from 'next/navigation';\nimport { useMemo } from 'react';\nimport { useLocale } from 'use-intl';\nimport { hasPathnamePrefixed, unprefixPathname, getLocalePrefix, getLocaleAsPrefix } from '../../shared/utils.js';\n\nfunction useBasePathname(config) {\n  // The types aren't entirely correct here. Outside of Next.js\n  // `useParams` can be called, but the return type is `null`.\n\n  // Notes on `useNextPathname`:\n  // - Types aren't entirely correct. Outside of Next.js the\n  //   hook will return `null` (e.g. unit tests)\n  // - A base path is stripped from the result\n  // - Rewrites *are* taken into account (i.e. the pathname\n  //   that the user sees in the browser is returned)\n  const pathname = usePathname();\n  const locale = useLocale();\n  return useMemo(() => {\n    if (!pathname) return pathname;\n    let unlocalizedPathname = pathname;\n    const prefix = getLocalePrefix(locale, config.localePrefix);\n    const isPathnamePrefixed = hasPathnamePrefixed(prefix, pathname);\n    if (isPathnamePrefixed) {\n      unlocalizedPathname = unprefixPathname(pathname, prefix);\n    } else if (config.localePrefix.mode === 'as-needed' && config.localePrefix.prefixes) {\n      // Workaround for https://github.com/vercel/next.js/issues/73085\n      const localeAsPrefix = getLocaleAsPrefix(locale);\n      if (hasPathnamePrefixed(localeAsPrefix, pathname)) {\n        unlocalizedPathname = unprefixPathname(pathname, localeAsPrefix);\n      }\n    }\n    return unlocalizedPathname;\n  }, [config.localePrefix, locale, pathname]);\n}\n\nexport { useBasePathname as default };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AAEA,SAAS,gBAAgB,MAAM;IAC7B,6DAA6D;IAC7D,4DAA4D;IAE5D,8BAA8B;IAC9B,0DAA0D;IAC1D,8CAA8C;IAC9C,4CAA4C;IAC5C,yDAAyD;IACzD,mDAAmD;IACnD,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,SAAS,CAAA,GAAA,qKAAA,CAAA,YAAS,AAAD;IACvB,OAAO,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;mCAAE;YACb,IAAI,CAAC,UAAU,OAAO;YACtB,IAAI,sBAAsB;YAC1B,MAAM,SAAS,CAAA,GAAA,gLAAA,CAAA,kBAAe,AAAD,EAAE,QAAQ,OAAO,YAAY;YAC1D,MAAM,qBAAqB,CAAA,GAAA,gLAAA,CAAA,sBAAmB,AAAD,EAAE,QAAQ;YACvD,IAAI,oBAAoB;gBACtB,sBAAsB,CAAA,GAAA,gLAAA,CAAA,mBAAgB,AAAD,EAAE,UAAU;YACnD,OAAO,IAAI,OAAO,YAAY,CAAC,IAAI,KAAK,eAAe,OAAO,YAAY,CAAC,QAAQ,EAAE;gBACnF,gEAAgE;gBAChE,MAAM,iBAAiB,CAAA,GAAA,gLAAA,CAAA,oBAAiB,AAAD,EAAE;gBACzC,IAAI,CAAA,GAAA,gLAAA,CAAA,sBAAmB,AAAD,EAAE,gBAAgB,WAAW;oBACjD,sBAAsB,CAAA,GAAA,gLAAA,CAAA,mBAAgB,AAAD,EAAE,UAAU;gBACnD;YACF;YACA,OAAO;QACT;kCAAG;QAAC,OAAO,YAAY;QAAE;QAAQ;KAAS;AAC5C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1501, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/praith/node_modules/next-intl/dist/esm/development/navigation/react-client/createNavigation.js"], "sourcesContent": ["import { useRouter, usePathname } from 'next/navigation';\nimport { useMemo } from 'react';\nimport { useLocale } from 'use-intl';\nimport createSharedNavigationFns from '../shared/createSharedNavigationFns.js';\nimport syncLocaleCookie from '../shared/syncLocaleCookie.js';\nimport { getRoute } from '../shared/utils.js';\nimport useBasePathname from './useBasePathname.js';\n\nfunction createNavigation(routing) {\n  const {\n    Link,\n    config,\n    getPathname,\n    ...redirects\n  } = createSharedNavigationFns(useLocale, routing);\n\n  /** @see https://next-intl.dev/docs/routing/navigation#usepathname */\n  function usePathname$1() {\n    const pathname = useBasePathname(config);\n    const locale = useLocale();\n\n    // @ts-expect-error -- Mirror the behavior from Next.js, where `null` is returned when `usePathname` is used outside of Next, but the types indicate that a string is always returned.\n    return useMemo(() => pathname &&\n    // @ts-expect-error -- This is fine\n    config.pathnames ? getRoute(locale, pathname,\n    // @ts-expect-error -- This is fine\n    config.pathnames) : pathname, [locale, pathname]);\n  }\n  function useRouter$1() {\n    const router = useRouter();\n    const curLocale = useLocale();\n    const nextPathname = usePathname();\n    return useMemo(() => {\n      function createHandler(fn) {\n        return function handler(href, options) {\n          const {\n            locale: nextLocale,\n            ...rest\n          } = options || {};\n          const pathname = getPathname({\n            href,\n            locale: nextLocale || curLocale\n          });\n          const args = [pathname];\n          if (Object.keys(rest).length > 0) {\n            // @ts-expect-error -- This is fine\n            args.push(rest);\n          }\n          syncLocaleCookie(config.localeCookie, nextPathname, curLocale, nextLocale);\n          fn(...args);\n        };\n      }\n      return {\n        ...router,\n        /** @see https://next-intl.dev/docs/routing/navigation#userouter */\n        push: createHandler(router.push),\n        /** @see https://next-intl.dev/docs/routing/navigation#userouter */\n        replace: createHandler(router.replace),\n        /** @see https://next-intl.dev/docs/routing/navigation#userouter */\n        prefetch: createHandler(router.prefetch)\n      };\n    }, [curLocale, nextPathname, router]);\n  }\n  return {\n    ...redirects,\n    Link,\n    usePathname: usePathname$1,\n    useRouter: useRouter$1,\n    getPathname\n  };\n}\n\nexport { createNavigation as default };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AAEA,SAAS,iBAAiB,OAAO;IAC/B,MAAM,EACJ,IAAI,EACJ,MAAM,EACN,WAAW,EACX,GAAG,WACJ,GAAG,CAAA,GAAA,kNAAA,CAAA,UAAyB,AAAD,EAAE,qKAAA,CAAA,YAAS,EAAE;IAEzC,mEAAmE,GACnE,SAAS;QACP,MAAM,WAAW,CAAA,GAAA,iNAAA,CAAA,UAAe,AAAD,EAAE;QACjC,MAAM,SAAS,CAAA,GAAA,qKAAA,CAAA,YAAS,AAAD;QAEvB,sLAAsL;QACtL,OAAO,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;sDAAE,IAAM,YACrB,mCAAmC;gBACnC,OAAO,SAAS,GAAG,CAAA,GAAA,8LAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ,UACpC,mCAAmC;gBACnC,OAAO,SAAS,IAAI;qDAAU;YAAC;YAAQ;SAAS;IAClD;IACA,SAAS;QACP,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;QACvB,MAAM,YAAY,CAAA,GAAA,qKAAA,CAAA,YAAS,AAAD;QAC1B,MAAM,eAAe,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;QAC/B,OAAO,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;oDAAE;gBACb,SAAS,cAAc,EAAE;oBACvB,OAAO,SAAS,QAAQ,IAAI,EAAE,OAAO;wBACnC,MAAM,EACJ,QAAQ,UAAU,EAClB,GAAG,MACJ,GAAG,WAAW,CAAC;wBAChB,MAAM,WAAW,YAAY;4BAC3B;4BACA,QAAQ,cAAc;wBACxB;wBACA,MAAM,OAAO;4BAAC;yBAAS;wBACvB,IAAI,OAAO,IAAI,CAAC,MAAM,MAAM,GAAG,GAAG;4BAChC,mCAAmC;4BACnC,KAAK,IAAI,CAAC;wBACZ;wBACA,CAAA,GAAA,yMAAA,CAAA,UAAgB,AAAD,EAAE,OAAO,YAAY,EAAE,cAAc,WAAW;wBAC/D,MAAM;oBACR;gBACF;gBACA,OAAO;oBACL,GAAG,MAAM;oBACT,iEAAiE,GACjE,MAAM,cAAc,OAAO,IAAI;oBAC/B,iEAAiE,GACjE,SAAS,cAAc,OAAO,OAAO;oBACrC,iEAAiE,GACjE,UAAU,cAAc,OAAO,QAAQ;gBACzC;YACF;mDAAG;YAAC;YAAW;YAAc;SAAO;IACtC;IACA,OAAO;QACL,GAAG,SAAS;QACZ;QACA,aAAa;QACb,WAAW;QACX;IACF;AACF", "ignoreList": [0], "debugId": null}}]}