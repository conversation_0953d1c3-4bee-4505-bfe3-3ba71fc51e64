'use client';

import { useTranslations } from 'next-intl';
import { Link } from '@/i18n/routing';
import Image from 'next/image';

export default function Navigation() {
  const t = useTranslations('navigation');

  return (
    <nav className="flex items-center justify-between px-6 py-4 bg-black text-white">
      {/* Logo */}
      <div className="flex items-center">
        <div className="text-2xl font-bold">
          <span className="text-green-400">∞</span>
          <span className="ml-1">Praith</span>
        </div>
      </div>

      {/* Navigation Links */}
      <div className="hidden md:flex items-center space-x-8">
        <Link href="/" className="hover:text-green-400 transition-colors">
          {t('home')}
        </Link>
        <Link href="/product" className="hover:text-green-400 transition-colors">
          {t('productPage')}
        </Link>
        <Link href="/contact" className="hover:text-green-400 transition-colors">
          {t('contactUs')}
        </Link>
      </div>

      {/* Right side buttons */}
      <div className="flex items-center space-x-4">
        <button className="bg-green-400 text-black px-6 py-2 rounded-md font-medium hover:bg-green-500 transition-colors">
          {t('getStarted')}
        </button>
        <div className="flex items-center space-x-2">
          <span className="text-sm">{t('language')}</span>
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        </div>
      </div>
    </nav>
  );
}
