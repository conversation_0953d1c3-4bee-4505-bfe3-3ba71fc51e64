'use client';

import { useTranslations } from 'next-intl';
import { Link } from '@/i18n/routing';

export default function Navigation() {
  const t = useTranslations('navigation');

  return (
    <nav className="flex items-center justify-between px-8 py-6 bg-transparent text-white relative z-20">
      {/* Logo */}
      <div className="flex items-center">
        <div className="text-2xl font-bold flex items-center">
          <span className="text-[#7ED321] text-2xl mr-1 transform rotate-90 font-black">∞</span>
          <span className="text-white font-bold">Praith</span>
        </div>
      </div>

      {/* Navigation Links */}
      <div className="hidden md:flex items-center space-x-12">
        <Link href="/" className="text-white hover:text-[#7ED321] transition-colors font-medium">
          Home
        </Link>
        <Link href="/product" className="text-white hover:text-[#7ED321] transition-colors font-medium">
          Product Page
        </Link>
        <Link href="/contact" className="text-white hover:text-[#7ED321] transition-colors font-medium">
          Contact Us
        </Link>
      </div>

      {/* Right side buttons */}
      <div className="flex items-center space-x-6">
        <button className="bg-[#7ED321] text-black px-6 py-2.5 rounded-md font-bold text-sm hover:bg-[#6BC91A] transition-colors">
          Get Started
        </button>
        <div className="flex items-center space-x-2">
          <span className="text-white text-sm font-medium">ENG</span>
          <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        </div>
      </div>
    </nav>
  );
}
