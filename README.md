# Praith - Blockchain Payment Gateway

A modern Next.js application showcasing a blockchain-powered crypto payment gateway with internationalization support.

## Features

- ✅ **Next.js 15** with App Router
- ✅ **TypeScript** for type safety
- ✅ **Tailwind CSS** for styling
- ✅ **Internationalization (i18n)** with next-intl
- ✅ **Responsive Design** optimized for all devices
- ✅ **Reusable Components** with modern React patterns
- ✅ **Multi-language Support** (English, Spanish, French, German)

## Project Structure

```
src/
├── app/
│   ├── [locale]/          # Locale-based routing
│   │   ├── layout.tsx     # Locale-specific layout
│   │   └── page.tsx       # Home page
│   ├── globals.css        # Global styles
│   ├── layout.tsx         # Root layout
│   └── not-found.tsx      # 404 page
├── components/
│   ├── CryptoLogos.tsx    # Cryptocurrency logos footer
│   ├── GlobalVisualization.tsx # Interactive world map
│   ├── HeroSection.tsx    # Main hero component
│   └── Navigation.tsx     # Navigation bar
├── i18n/
│   ├── request.ts         # i18n request configuration
│   └── routing.ts         # Routing configuration
├── messages/              # Translation files
│   ├── en.json           # English translations
│   ├── es.json           # Spanish translations
│   ├── fr.json           # French translations
│   └── de.json           # German translations
└── middleware.ts          # Next.js middleware for i18n
```

## Getting Started

1. **Install dependencies:**
   ```bash
   npm install
   ```

2. **Run the development server:**
   ```bash
   npm run dev
   ```

3. **Open your browser:**
   Navigate to [http://localhost:3000](http://localhost:3000)

## Internationalization

The application supports multiple languages with automatic locale detection:

- **English** (`/en`) - Default locale
- **Spanish** (`/es`)
- **French** (`/fr`)
- **German** (`/de`)

### Adding New Languages

1. Create a new translation file in `src/messages/[locale].json`
2. Add the locale to `src/i18n/routing.ts`
3. Update the middleware matcher in `src/middleware.ts`

## Components

### HeroSection
The main hero component featuring:
- Animated global visualization
- Cryptocurrency icons
- Responsive design
- Internationalized content

### Navigation
Responsive navigation bar with:
- Logo and branding
- Multi-language menu items
- Language selector
- Call-to-action button

### GlobalVisualization
Interactive world map component with:
- Animated orbital rings
- Floating cryptocurrency icons
- Connection lines
- Dotted world map background

### CryptoLogos
Footer component displaying supported cryptocurrencies:
- Tether (USDT)
- Bitcoin (BTC)
- Tron (TRX)
- Ethereum (ETH)
- Litecoin (LTC)

## Technologies Used

- **Next.js 15** - React framework with App Router
- **TypeScript** - Type-safe JavaScript
- **Tailwind CSS** - Utility-first CSS framework
- **next-intl** - Internationalization for Next.js
- **React** - UI library

## Development

### Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint

### Code Style

The project follows modern React and TypeScript best practices:
- Functional components with hooks
- TypeScript strict mode
- ESLint configuration
- Tailwind CSS for styling

## Deployment

The application can be deployed on any platform that supports Next.js:

- **Vercel** (recommended)
- **Netlify**
- **AWS**
- **Docker**

For Vercel deployment:
```bash
npm run build
```

## License

This project is open source and available under the [MIT License](LICENSE).
