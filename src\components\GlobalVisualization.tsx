'use client';

export default function GlobalVisualization() {
  return (
    <div className="relative w-full h-96 lg:h-[500px]">
      {/* World Map Background (Dotted Pattern) */}
      <div className="absolute inset-0 opacity-30">
        <svg
          viewBox="0 0 800 500"
          className="w-full h-full"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          {/* Dotted world map pattern */}
          <defs>
            <pattern id="dots" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse">
              <circle cx="10" cy="10" r="1" fill="#374151" />
            </pattern>
          </defs>
          <rect width="800" height="500" fill="url(#dots)" />
          
          {/* Simplified continent shapes with dots */}
          <g fill="#4B5563">
            {/* North America */}
            <circle cx="150" cy="120" r="2" />
            <circle cx="170" cy="140" r="2" />
            <circle cx="190" cy="160" r="2" />
            
            {/* Europe */}
            <circle cx="400" cy="100" r="2" />
            <circle cx="420" cy="120" r="2" />
            
            {/* Asia */}
            <circle cx="600" cy="140" r="2" />
            <circle cx="650" cy="160" r="2" />
            
            {/* Africa */}
            <circle cx="450" cy="250" r="2" />
            <circle cx="470" cy="280" r="2" />
            
            {/* South America */}
            <circle cx="250" cy="300" r="2" />
            <circle cx="270" cy="350" r="2" />
            
            {/* Australia */}
            <circle cx="700" cy="350" r="2" />
          </g>
        </svg>
      </div>

      {/* Central Hub */}
      <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
        <div className="relative">
          {/* Central green circle with icons */}
          <div className="w-20 h-20 bg-green-400 rounded-full flex items-center justify-center relative">
            <div className="w-8 h-8 bg-white rounded-sm flex items-center justify-center">
              <span className="text-green-600 font-bold text-lg">₿</span>
            </div>
            <div className="absolute -top-2 -right-2 w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
              <span className="text-white text-xs">⚡</span>
            </div>
          </div>
        </div>
      </div>

      {/* Floating Crypto Icons */}
      <div className="absolute top-16 left-16">
        <div className="w-12 h-12 bg-red-500 rounded-full flex items-center justify-center animate-pulse">
          <span className="text-white font-bold">₿</span>
        </div>
      </div>

      <div className="absolute top-32 right-20">
        <div className="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center animate-pulse">
          <span className="text-white font-bold">Ξ</span>
        </div>
      </div>

      <div className="absolute bottom-32 left-20">
        <div className="w-12 h-12 bg-teal-500 rounded-full flex items-center justify-center animate-pulse">
          <span className="text-white font-bold">T</span>
        </div>
      </div>

      <div className="absolute bottom-20 right-32">
        <div className="w-12 h-12 bg-orange-500 rounded-full flex items-center justify-center animate-pulse">
          <span className="text-white font-bold">Ł</span>
        </div>
      </div>

      {/* Orbital Rings */}
      <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
        <div className="w-64 h-64 border border-green-400 rounded-full opacity-30 animate-spin" style={{animationDuration: '20s'}}></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-80 h-80 border border-green-400 rounded-full opacity-20 animate-spin" style={{animationDuration: '30s', animationDirection: 'reverse'}}></div>
      </div>

      {/* Connection Lines */}
      <svg className="absolute inset-0 w-full h-full pointer-events-none">
        <defs>
          <linearGradient id="lineGradient" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" stopColor="#10B981" stopOpacity="0" />
            <stop offset="50%" stopColor="#10B981" stopOpacity="0.8" />
            <stop offset="100%" stopColor="#10B981" stopOpacity="0" />
          </linearGradient>
        </defs>
        
        {/* Animated connection lines */}
        <line x1="50%" y1="50%" x2="10%" y2="20%" stroke="url(#lineGradient)" strokeWidth="2" className="animate-pulse" />
        <line x1="50%" y1="50%" x2="90%" y2="30%" stroke="url(#lineGradient)" strokeWidth="2" className="animate-pulse" />
        <line x1="50%" y1="50%" x2="20%" y2="80%" stroke="url(#lineGradient)" strokeWidth="2" className="animate-pulse" />
        <line x1="50%" y1="50%" x2="80%" y2="85%" stroke="url(#lineGradient)" strokeWidth="2" className="animate-pulse" />
      </svg>
    </div>
  );
}
