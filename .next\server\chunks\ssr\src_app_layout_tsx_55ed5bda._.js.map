{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/praith/src/app/layout.tsx"], "sourcesContent": ["// Since we have a `not-found.tsx` page on the root, a layout file\n// is required, even if it's just passing children through.\nexport default function RootLayout({\n  children,\n}: {\n  children: React.ReactNode;\n}) {\n  return children;\n}\n"], "names": [], "mappings": "AAAA,kEAAkE;AAClE,2DAA2D;;;;AAC5C,SAAS,WAAW,EACjC,QAAQ,EAGT;IACC,OAAO;AACT", "debugId": null}}]}